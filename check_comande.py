#!/usr/bin/env python3
"""
Script per verificare le comande nel database
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.database_pg import database_connection

def check_comande():
    """Verifica le comande nel database"""
    try:
        with database_connection() as (conn, cursor):

            print("🔍 Verifica comande nel database...")

            # Verifica se la tabella esiste
            cursor.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables
                    WHERE table_name = 'comande'
                );
            """)

            table_exists = cursor.fetchone()[0]
            if not table_exists:
                print("❌ Tabella 'comande' non trovata")
                return

            print("✅ Tabella 'comande' trovata")

            # Conta tutte le comande
            cursor.execute("SELECT COUNT(*) FROM comande;")
            total_count = cursor.fetchone()[0]
            print(f"📊 Totale comande: {total_count}")

            if total_count == 0:
                print("ℹ️ Nessuna comanda trovata nel database")
                return

            # Mostra le comande per cantiere
            cursor.execute("""
                SELECT id_cantiere, COUNT(*) as count
                FROM comande
                GROUP BY id_cantiere
                ORDER BY id_cantiere;
            """)

            print("\n📋 Comande per cantiere:")
            for row in cursor.fetchall():
                cantiere_id, count = row
                print(f"  Cantiere {cantiere_id}: {count} comande")

            # Mostra le comande per stato
            cursor.execute("""
                SELECT stato, COUNT(*) as count
                FROM comande
                GROUP BY stato
                ORDER BY stato;
            """)

            print("\n📊 Comande per stato:")
            for row in cursor.fetchall():
                stato, count = row
                print(f"  {stato}: {count} comande")

            # Mostra dettagli delle prime 5 comande
            cursor.execute("""
                SELECT codice_comanda, tipo_comanda, stato, responsabile, data_creazione, id_cantiere
                FROM comande
                ORDER BY data_creazione DESC
                LIMIT 5;
            """)

            print("\n📋 Prime 5 comande (più recenti):")
            for row in cursor.fetchall():
                codice, tipo, stato, responsabile, data, cantiere = row
                print(f"  {codice}: {tipo} - {stato} - {responsabile} - {data} (Cantiere {cantiere})")

            # Verifica specificamente il cantiere 1
            cursor.execute("""
                SELECT codice_comanda, tipo_comanda, stato, responsabile
                FROM comande
                WHERE id_cantiere = 1
                ORDER BY data_creazione DESC;
            """)

            comande_cantiere_1 = cursor.fetchall()
            print(f"\n🏗️ Comande del cantiere 1: {len(comande_cantiere_1)}")
            for row in comande_cantiere_1:
                codice, tipo, stato, responsabile = row
                print(f"  {codice}: {tipo} - {stato} - {responsabile}")

    except Exception as e:
        print(f"❌ Errore: {e}")

if __name__ == "__main__":
    check_comande()
