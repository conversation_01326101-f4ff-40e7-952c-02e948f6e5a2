#!/usr/bin/env python3
"""
Test del flusso completo delle comande per verificare:
1. Creazione comanda di posa
2. Aggiornamento stato cavi a "In corso"
3. Assegnazione responsabile
4. Verifica che l'utente veda il cavo come assegnato
"""

import sys
import os
sys.path.append('.')
sys.path.append('./modules')
sys.path.append('./webapp/backend')

from modules.comande_new import crea_comanda_con_cavi, ottieni_cavi_comanda
import psycopg2
import psycopg2.extras
import json

def get_db_connection():
    """Ottiene una connessione diretta al database"""
    return psycopg2.connect(
        host="localhost",
        database="cantieri",
        user="postgres",
        password="Taranto",
        cursor_factory=psycopg2.extras.RealDictCursor
    )

def test_creazione_comanda_posa():
    """Test completo del flusso di creazione comanda di posa"""
    print("🧪 === TEST FLUSSO COMANDE DI POSA ===")
    
    # Parametri di test
    id_cantiere = 1
    responsabile = "<PERSON> Rossi"
    email = "<EMAIL>"
    telefono = "+39 123 456 789"
    
    print(f"📋 Parametri test:")
    print(f"   - Cantiere: {id_cantiere}")
    print(f"   - Responsabile: {responsabile}")
    print(f"   - Email: {email}")
    print(f"   - Telefono: {telefono}")
    
    try:
        # 1. Verifica cavi disponibili nel cantiere
        print("\n🔍 1. Verifica cavi disponibili...")
        conn = get_db_connection()
        try:
            c = conn.cursor()
            c.execute("SELECT id_cavo FROM Cavi WHERE id_cantiere = %s LIMIT 5", (id_cantiere,))
            cavi_cantiere = [dict(row) for row in c.fetchall()]
        finally:
            conn.close()
        print(f"   Trovati {len(cavi_cantiere)} cavi nel cantiere")
        
        if len(cavi_cantiere) == 0:
            print("❌ Nessun cavo trovato nel cantiere. Impossibile procedere con il test.")
            return False
            
        # Prendi i primi 2 cavi disponibili per il test
        cavi_test = [cavo['id_cavo'] for cavo in cavi_cantiere[:2]]
        print(f"   Cavi selezionati per test: {cavi_test}")
        
        # 2. Verifica stato iniziale dei cavi
        print("\n🔍 2. Verifica stato iniziale cavi...")
        with database_connection() as conn:
            c = conn.cursor()
            for id_cavo in cavi_test:
                c.execute("""
                    SELECT id_cavo, stato_installazione, comanda_posa 
                    FROM Cavi 
                    WHERE id_cavo = %s AND id_cantiere = %s
                """, (id_cavo, id_cantiere))
                result = c.fetchone()
                if result:
                    print(f"   {result['id_cavo']}: stato={result['stato_installazione']}, comanda_posa={result['comanda_posa']}")
                else:
                    print(f"   ❌ Cavo {id_cavo} non trovato!")
        
        # 3. Crea comanda di posa
        print("\n🚀 3. Creazione comanda di posa...")
        codice_comanda = crea_comanda_con_cavi(
            id_cantiere=id_cantiere,
            tipo_comanda='POSA',
            descrizione='Test comanda posa automatica',
            responsabile=responsabile,
            lista_id_cavi=cavi_test,
            responsabile_email=email,
            responsabile_telefono=telefono
        )
        
        if not codice_comanda:
            print("❌ Errore nella creazione della comanda!")
            return False
            
        print(f"   ✅ Comanda creata: {codice_comanda}")
        
        # 4. Verifica stato finale dei cavi
        print("\n🔍 4. Verifica stato finale cavi...")
        with database_connection() as conn:
            c = conn.cursor()
            cavi_in_corso = 0
            for id_cavo in cavi_test:
                c.execute("""
                    SELECT id_cavo, stato_installazione, comanda_posa 
                    FROM Cavi 
                    WHERE id_cavo = %s AND id_cantiere = %s
                """, (id_cavo, id_cantiere))
                result = c.fetchone()
                if result:
                    print(f"   {result['id_cavo']}: stato={result['stato_installazione']}, comanda_posa={result['comanda_posa']}")
                    if result['stato_installazione'] == 'In corso':
                        cavi_in_corso += 1
                else:
                    print(f"   ❌ Cavo {id_cavo} non trovato!")
        
        # 5. Verifica comanda creata
        print("\n🔍 5. Verifica dettagli comanda...")
        with database_connection() as conn:
            c = conn.cursor()
            c.execute("""
                SELECT codice_comanda, tipo_comanda, stato, responsabile, descrizione
                FROM Comande 
                WHERE codice_comanda = %s
            """, (codice_comanda,))
            result = c.fetchone()
            if result:
                print(f"   Codice: {result['codice_comanda']}")
                print(f"   Tipo: {result['tipo_comanda']}")
                print(f"   Stato: {result['stato']}")
                print(f"   Responsabile: {result['responsabile']}")
                print(f"   Descrizione: {result['descrizione']}")
            else:
                print("   ❌ Comanda non trovata!")
                return False
        
        # 6. Verifica cavi assegnati alla comanda
        print("\n🔍 6. Verifica cavi assegnati alla comanda...")
        cavi_comanda = ottieni_cavi_comanda(codice_comanda)
        print(f"   Cavi assegnati alla comanda: {len(cavi_comanda)}")
        for cavo in cavi_comanda:
            print(f"   - {cavo['id_cavo']}: {cavo['stato_installazione']}")
        
        # 7. Risultati del test
        print("\n📊 === RISULTATI TEST ===")
        print(f"✅ Comanda creata: {codice_comanda}")
        print(f"✅ Cavi con stato 'In corso': {cavi_in_corso}/{len(cavi_test)}")
        print(f"✅ Cavi assegnati alla comanda: {len(cavi_comanda)}")
        
        if cavi_in_corso == len(cavi_test) and len(cavi_comanda) == len(cavi_test):
            print("🎉 TEST SUPERATO! Il flusso delle comande funziona correttamente.")
            print("   - I cavi sono stati correttamente impostati come 'In corso'")
            print("   - I cavi sono stati assegnati al responsabile")
            print("   - L'utente può vedere che i cavi sono assegnati a una comanda")
            return True
        else:
            print("❌ TEST FALLITO! Problemi nel flusso delle comande.")
            return False
            
    except Exception as e:
        print(f"❌ Errore durante il test: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_eliminazione_comanda():
    """Test eliminazione comanda e ripristino stato cavi"""
    print("\n🧪 === TEST ELIMINAZIONE COMANDA ===")
    
    try:
        # Trova una comanda di test da eliminare
        with database_connection() as conn:
            c = conn.cursor()
            c.execute("""
                SELECT codice_comanda, tipo_comanda 
                FROM Comande 
                WHERE tipo_comanda = 'POSA' AND stato = 'ASSEGNATA'
                LIMIT 1
            """)
            result = c.fetchone()
            
            if not result:
                print("   ℹ️ Nessuna comanda di posa da testare per l'eliminazione")
                return True
                
            codice_comanda = result['codice_comanda']
            print(f"   Comanda da eliminare: {codice_comanda}")
            
            # Verifica cavi prima dell'eliminazione
            cavi_prima = ottieni_cavi_comanda(codice_comanda)
            print(f"   Cavi assegnati prima: {len(cavi_prima)}")
            
            # Elimina la comanda
            from modules.comande_new import elimina_comanda
            successo = elimina_comanda(codice_comanda)
            
            if successo:
                print("   ✅ Comanda eliminata con successo")
                
                # Verifica che i cavi siano tornati allo stato precedente
                for cavo in cavi_prima:
                    c.execute("""
                        SELECT stato_installazione, comanda_posa 
                        FROM Cavi 
                        WHERE id_cavo = %s
                    """, (cavo['id_cavo'],))
                    result = c.fetchone()
                    if result:
                        print(f"   {cavo['id_cavo']}: stato={result['stato_installazione']}, comanda_posa={result['comanda_posa']}")
                
                print("   ✅ Test eliminazione completato")
                return True
            else:
                print("   ❌ Errore nell'eliminazione della comanda")
                return False
                
    except Exception as e:
        print(f"❌ Errore durante test eliminazione: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 Avvio test sistema comande...")
    
    # Test 1: Creazione comanda
    test1_ok = test_creazione_comanda_posa()
    
    # Test 2: Eliminazione comanda
    test2_ok = test_eliminazione_comanda()
    
    print("\n" + "="*60)
    print("📋 RIEPILOGO TEST")
    print("="*60)
    print(f"Test creazione comanda: {'✅ PASS' if test1_ok else '❌ FAIL'}")
    print(f"Test eliminazione comanda: {'✅ PASS' if test2_ok else '❌ FAIL'}")
    
    if test1_ok and test2_ok:
        print("\n🎉 TUTTI I TEST SUPERATI!")
        print("Il sistema comande funziona correttamente.")
    else:
        print("\n❌ ALCUNI TEST FALLITI!")
        print("Verificare i problemi nel sistema comande.")
