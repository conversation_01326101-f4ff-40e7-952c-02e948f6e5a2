#!/usr/bin/env python3
"""
Test per forzare il ricaricamento del modulo e testare i nuovi codici
"""

import sys
import os
import importlib
sys.path.append('.')
sys.path.append('./modules')

def test_ricarica_modulo():
    """Test ricaricamento modulo e generazione nuovo codice"""
    print("🧪 === TEST RICARICAMENTO MODULO E NUOVI CODICI ===")
    
    try:
        # Forza il ricaricamento del modulo
        if 'modules.comande_new' in sys.modules:
            print("🔄 Ricaricamento modulo comande_new...")
            importlib.reload(sys.modules['modules.comande_new'])
        
        from modules.comande_new import genera_codice_comanda, crea_comanda
        
        # Test generazione diretta del codice
        print("\n🎯 Test generazione diretta codice:")
        for tipo in ['POSA', 'COLLEGAMENTO_PARTENZA', 'CERTIFICAZIONE']:
            try:
                codice = genera_codice_comanda(1, tipo)
                print(f"   {tipo:20} → {codice}")
                
                # Verifica formato
                formato_ok = (
                    len(codice) <= 6 and
                    codice[:3].isalpha() and
                    (codice[3:].isdigit() if len(codice) > 3 else True)
                )
                print(f"     Formato: {'✅ OK' if formato_ok else '❌ ERRORE'}")
                
            except Exception as e:
                print(f"   {tipo:20} → ❌ Errore: {e}")
        
        # Test creazione comanda completa
        print(f"\n🚀 Test creazione comanda completa:")
        try:
            codice_comanda = crea_comanda(
                id_cantiere=1,
                tipo_comanda='POSA',
                descrizione='Test nuovo formato codice',
                responsabile='Test Responsabile Nuovo'
            )
            
            if codice_comanda:
                print(f"✅ Comanda creata: {codice_comanda}")
                
                # Verifica formato
                formato_ok = (
                    len(codice_comanda) <= 6 and
                    codice_comanda.startswith('POS') and
                    codice_comanda[3:].isdigit()
                )
                
                print(f"✅ Formato nuovo: {'SÌ' if formato_ok else 'NO'}")
                print(f"✅ Lunghezza: {len(codice_comanda)} caratteri")
                
                if not formato_ok:
                    print(f"⚠️ ATTENZIONE: Il codice '{codice_comanda}' usa ancora il formato vecchio!")
                    print(f"   Possibili cause:")
                    print(f"   - Il backend non ha ricaricato il modulo")
                    print(f"   - C'è un errore nella funzione genera_codice_comanda")
                    print(f"   - Il database non è accessibile (fallback al timestamp)")
                
                return formato_ok, codice_comanda
            else:
                print("❌ Errore nella creazione della comanda")
                return False, None
                
        except Exception as e:
            print(f"❌ Errore nella creazione: {e}")
            return False, None
            
    except Exception as e:
        print(f"❌ Errore generale: {e}")
        import traceback
        traceback.print_exc()
        return False, None

def test_api_backend():
    """Test API backend per verificare se usa i nuovi codici"""
    print("\n🌐 === TEST API BACKEND ===")
    
    try:
        import requests
        
        # Test creazione comanda via API
        print("🔗 Test creazione comanda via API...")
        
        payload = {
            "id_cantiere": 1,
            "tipo_comanda": "POSA",
            "descrizione": "Test API nuovo formato",
            "responsabile": "Test API",
            "responsabile_email": "<EMAIL>"
        }
        
        response = requests.post(
            "http://localhost:8001/api/comande/",
            json=payload,
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            codice_comanda = result.get('codice_comanda', '')
            
            print(f"✅ API risposta: {result}")
            print(f"✅ Codice generato: {codice_comanda}")
            
            # Verifica formato
            formato_ok = (
                len(codice_comanda) <= 6 and
                codice_comanda.startswith('POS') and
                codice_comanda[3:].isdigit()
            )
            
            print(f"✅ Formato nuovo via API: {'SÌ' if formato_ok else 'NO'}")
            
            if not formato_ok:
                print(f"⚠️ BACKEND NON AGGIORNATO!")
                print(f"   Il backend sta ancora usando il formato vecchio")
                print(f"   Riavvia il backend per applicare le modifiche")
            
            return formato_ok, codice_comanda
        else:
            print(f"❌ API errore: {response.status_code} - {response.text}")
            return False, None
            
    except Exception as e:
        print(f"❌ Errore API: {e}")
        return False, None

def main():
    print("🚀 AVVIO TEST NUOVO FORMATO CODICI")
    print("="*50)
    
    # Test 1: Ricaricamento modulo
    test1_ok, codice1 = test_ricarica_modulo()
    
    # Test 2: API Backend
    test2_ok, codice2 = test_api_backend()
    
    # Risultati
    print("\n" + "="*50)
    print("📋 RIEPILOGO TEST")
    print("="*50)
    print(f"✅ Modulo locale: {'NUOVO FORMATO' if test1_ok else 'FORMATO VECCHIO'}")
    print(f"✅ API Backend: {'NUOVO FORMATO' if test2_ok else 'FORMATO VECCHIO'}")
    
    if test1_ok and test2_ok:
        print(f"\n🎉 SUCCESSO! NUOVO FORMATO ATTIVO")
        print(f"   Codice locale: {codice1}")
        print(f"   Codice API: {codice2}")
        print(f"\n✅ Il sistema ora genera codici nel formato:")
        print(f"   POS001, POS002, CPT001, CAR001, CER001")
        
    elif test1_ok and not test2_ok:
        print(f"\n⚠️ BACKEND NON AGGIORNATO")
        print(f"   Il modulo locale funziona: {codice1}")
        print(f"   Ma il backend usa ancora il formato vecchio")
        print(f"\n🔧 SOLUZIONE:")
        print(f"   1. Ferma il backend (Ctrl+C)")
        print(f"   2. Riavvia: python webapp/backend/main.py")
        print(f"   3. Testa di nuovo")
        
    else:
        print(f"\n❌ PROBLEMI RILEVATI")
        print(f"   Verificare la configurazione del sistema")
    
    return test1_ok and test2_ok

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
