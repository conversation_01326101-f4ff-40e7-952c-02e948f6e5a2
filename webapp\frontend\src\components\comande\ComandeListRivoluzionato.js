import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Button,
  Paper,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Alert,
  CircularProgress,
  Tooltip,
  List,
  ListItem,
  ListItemText,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Stack,
  MenuItem,
  Divider
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Assignment as AssignIcon,
  Person as PersonIcon,
  Email as EmailIcon,
  Phone as PhoneIcon,
  ExpandMore as ExpandMoreIcon,
  CheckCircle as CheckCircleIcon,
  Verified as VerifiedIcon,
  Visibility as ViewIcon
} from '@mui/icons-material';
import comandeService from '../../services/comandeService';
import responsabiliService from '../../services/responsabiliService';
import CreaComandaConCavi from './CreaComandaConCavi';

const ComandeListRivoluzionato = ({ cantiereId, cantiereName }) => {
  // Stati principali - Responsabili come elemento principale
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Stati comande
  const [statistiche, setStatistiche] = useState(null);
  const [openCreaConCavi, setOpenCreaConCavi] = useState(false);

  // Stati responsabili
  const [responsabili, setResponsabili] = useState([]);
  const [loadingResponsabili, setLoadingResponsabili] = useState(false);
  const [comandePerResponsabile, setComandePerResponsabile] = useState({});
  const [openResponsabileDialog, setOpenResponsabileDialog] = useState(false);
  const [dialogModeResponsabile, setDialogModeResponsabile] = useState('create');
  const [selectedResponsabile, setSelectedResponsabile] = useState(null);
  const [formDataResponsabile, setFormDataResponsabile] = useState({
    nome_responsabile: '',
    email: '',
    telefono: ''
  });

  // Stati per dialog comande
  const [openComandaDialog, setOpenComandaDialog] = useState(false);
  const [dialogModeComanda, setDialogModeComanda] = useState('view'); // 'view', 'edit'
  const [selectedComanda, setSelectedComanda] = useState(null);
  const [formDataComanda, setFormDataComanda] = useState({
    tipo_comanda: 'POSA',
    descrizione: '',
    responsabile: '',
    data_scadenza: '',
    priorita: 'NORMALE',
    note_capo_cantiere: ''
  });

  const loadComande = async () => {
    try {
      setLoading(true);
      // Non serve più salvare le comande in uno stato separato
      // Le comande vengono caricate per responsabile
      setError(null);
    } catch (err) {
      console.error('Errore nel caricamento delle comande:', err);
      setError('Errore nel caricamento delle comande');
    } finally {
      setLoading(false);
    }
  };

  const loadStatistiche = async () => {
    try {
      const stats = await comandeService.getStatisticheComande(cantiereId);
      setStatistiche(stats);
    } catch (err) {
      console.error('Errore nel caricamento delle statistiche:', err);
    }
  };

  const loadResponsabili = async () => {
    try {
      setLoadingResponsabili(true);
      setError(null);

      const data = await responsabiliService.getResponsabiliCantiere(cantiereId);
      setResponsabili(data || []);
      await loadComandePerResponsabili(data || []);
    } catch (err) {
      console.error('Errore nel caricamento dei responsabili:', err);
      const errorMessage = err.response?.data?.detail || err.message || 'Errore nel caricamento dei responsabili';
      setError(`Errore nel caricamento dei responsabili: ${errorMessage}`);
    } finally {
      setLoadingResponsabili(false);
    }
  };

  // Carica dati al mount - Focus sui responsabili
  useEffect(() => {
    if (cantiereId) {
      loadResponsabili();
      loadComande();
      loadStatistiche();
    }
  }, [cantiereId]);

  const loadComandePerResponsabili = async (responsabiliList) => {
    try {
      const comandeMap = {};
      for (const responsabile of responsabiliList) {
        try {
          const response = await comandeService.getComandeByResponsabile(cantiereId, responsabile.nome_responsabile);
          // Assicurati che sia sempre un array
          let comande = [];
          if (response && Array.isArray(response)) {
            comande = response;
          } else if (response && response.comande && Array.isArray(response.comande)) {
            comande = response.comande;
          } else if (response && response.data && Array.isArray(response.data)) {
            comande = response.data;
          }
          comandeMap[responsabile.id_responsabile] = comande;
        } catch (err) {
          console.error(`Errore nel caricamento comande per ${responsabile.nome_responsabile}:`, err);
          comandeMap[responsabile.id_responsabile] = [];
        }
      }
      setComandePerResponsabile(comandeMap);
    } catch (err) {
      console.error('Errore nel caricamento delle comande:', err);
    }
  };

  // Gestione responsabili
  const handleOpenResponsabileDialog = (mode, responsabile = null) => {
    setDialogModeResponsabile(mode);
    setSelectedResponsabile(responsabile);
    
    if (mode === 'edit' && responsabile) {
      setFormDataResponsabile({
        nome_responsabile: responsabile.nome_responsabile || '',
        email: responsabile.email || '',
        telefono: responsabile.telefono || ''
      });
    } else {
      setFormDataResponsabile({
        nome_responsabile: '',
        email: '',
        telefono: ''
      });
    }
    
    setOpenResponsabileDialog(true);
  };

  const handleCloseResponsabileDialog = () => {
    setOpenResponsabileDialog(false);
    setSelectedResponsabile(null);
    setError(null);
  };

  const handleSubmitResponsabile = async () => {
    try {
      setError(null);
      
      if (!formDataResponsabile.nome_responsabile.trim()) {
        setError('Il nome del responsabile è obbligatorio');
        return;
      }
      
      if (!formDataResponsabile.email && !formDataResponsabile.telefono) {
        setError('Almeno uno tra email e telefono deve essere specificato');
        return;
      }

      if (dialogModeResponsabile === 'create') {
        await responsabiliService.createResponsabile(cantiereId, formDataResponsabile);
      } else if (dialogModeResponsabile === 'edit') {
        await responsabiliService.updateResponsabile(selectedResponsabile.id_responsabile, formDataResponsabile);
      }

      handleCloseResponsabileDialog();
      await loadResponsabili();
    } catch (err) {
      console.error('Errore nel salvataggio:', err);
      setError(err.detail || 'Errore nel salvataggio del responsabile');
    }
  };

  const handleDeleteResponsabile = async (idResponsabile) => {
    if (!window.confirm('Sei sicuro di voler eliminare questo responsabile?')) {
      return;
    }

    try {
      await responsabiliService.deleteResponsabile(idResponsabile);
      await loadResponsabili();
    } catch (err) {
      console.error('Errore nell\'eliminazione:', err);
      setError('Errore nell\'eliminazione del responsabile');
    }
  };

  // Gestione comande
  const handleOpenComandaDialog = (mode, comanda = null) => {
    setDialogModeComanda(mode);
    setSelectedComanda(comanda);

    if (mode === 'edit' && comanda) {
      setFormDataComanda({
        tipo_comanda: comanda.tipo_comanda,
        descrizione: comanda.descrizione || '',
        responsabile: comanda.responsabile || '',
        data_scadenza: comanda.data_scadenza || '',
        priorita: comanda.priorita || 'NORMALE',
        note_capo_cantiere: comanda.note_capo_cantiere || ''
      });
    }

    setOpenComandaDialog(true);
  };

  const handleCloseComandaDialog = () => {
    setOpenComandaDialog(false);
    setSelectedComanda(null);
    setFormDataComanda({
      tipo_comanda: 'POSA',
      descrizione: '',
      responsabile: '',
      data_scadenza: '',
      priorita: 'NORMALE',
      note_capo_cantiere: ''
    });
  };

  const handleSubmitComanda = async () => {
    try {
      if (dialogModeComanda === 'edit') {
        await comandeService.updateComanda(selectedComanda.codice_comanda, formDataComanda);
        handleCloseComandaDialog();
        await loadResponsabili(); // Ricarica per aggiornare le comande
        await loadStatistiche();
      }
    } catch (err) {
      console.error('Errore nel salvataggio:', err);
      setError('Errore nel salvataggio della comanda');
    }
  };

  const handleDeleteComanda = async (codiceComanda) => {
    if (!window.confirm('Sei sicuro di voler eliminare questa comanda?')) {
      return;
    }

    try {
      await comandeService.deleteComanda(codiceComanda);
      await loadResponsabili(); // Ricarica per aggiornare le comande
      await loadStatistiche();
    } catch (err) {
      console.error('Errore nell\'eliminazione:', err);
      setError('Errore nell\'eliminazione della comanda');
    }
  };

  const getTipoComandaLabel = (tipo) => {
    const labels = {
      'POSA': 'Posa',
      'COLLEGAMENTO_PARTENZA': 'Coll. Partenza',
      'COLLEGAMENTO_ARRIVO': 'Coll. Arrivo',
      'CERTIFICAZIONE': 'Certificazione',
      'TESTING': 'Testing/Certificazione'
    };
    return labels[tipo] || tipo;
  };

  const getStatoColor = (stato) => {
    const colors = {
      'CREATA': 'default',
      'ASSEGNATA': 'primary',
      'IN_CORSO': 'warning',
      'COMPLETATA': 'success',
      'ANNULLATA': 'error'
    };
    return colors[stato] || 'default';
  };

  const getPrioritaColor = (priorita) => {
    const colors = {
      'BASSA': 'default',
      'NORMALE': 'primary',
      'ALTA': 'warning',
      'URGENTE': 'error'
    };
    return colors[priorita] || 'default';
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box mb={3}>
        <Typography variant="h4" sx={{ fontWeight: 600, color: 'primary.main' }}>
          {cantiereName}
        </Typography>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {/* Statistiche in stile Visualizza Cavi */}
      {statistiche && (
        <Paper sx={{ p: 2, mb: 3, bgcolor: 'grey.50' }}>
          <Stack direction="row" spacing={4} alignItems="center" justifyContent="space-between" flexWrap="wrap">
            {/* Responsabili */}
            <Stack direction="row" alignItems="center" spacing={1}>
              <PersonIcon color="primary" fontSize="small" />
              <Box>
                <Typography variant="h6" fontWeight="bold" sx={{ lineHeight: 1 }}>
                  {statistiche.responsabili_attivi || 0}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  Responsabili
                </Typography>
              </Box>
            </Stack>

            {/* Totale Comande */}
            <Stack direction="row" alignItems="center" spacing={1}>
              <AssignIcon color="info" fontSize="small" />
              <Box>
                <Typography variant="h6" fontWeight="bold" sx={{ lineHeight: 1 }}>
                  {statistiche.totale_comande || 0}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  Totale
                </Typography>
              </Box>
            </Stack>

            {/* In Corso */}
            <Stack direction="row" alignItems="center" spacing={1}>
              <CheckCircleIcon color="warning" fontSize="small" />
              <Box>
                <Typography variant="h6" fontWeight="bold" sx={{ lineHeight: 1 }}>
                  {statistiche.comande_in_corso || 0}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  In Corso
                </Typography>
              </Box>
            </Stack>

            {/* Completate */}
            <Stack direction="row" alignItems="center" spacing={1}>
              <VerifiedIcon color="success" fontSize="small" />
              <Box>
                <Typography variant="h6" fontWeight="bold" sx={{ lineHeight: 1 }}>
                  {statistiche.comande_completate || 0}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  Completate
                </Typography>
              </Box>
            </Stack>

            {/* Percentuale completamento */}
            <Stack direction="row" alignItems="center" spacing={1}>
              <Box sx={{
                width: 32,
                height: 32,
                borderRadius: '50%',
                bgcolor: (statistiche.comande_completate / (statistiche.totale_comande || 1)) >= 0.8 ? 'success.main' :
                         (statistiche.comande_completate / (statistiche.totale_comande || 1)) >= 0.5 ? 'warning.main' : 'error.main',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}>
                <Typography variant="caption" fontWeight="bold" color="white">
                  {statistiche.totale_comande > 0 ? Math.round((statistiche.comande_completate / statistiche.totale_comande) * 100) : 0}%
                </Typography>
              </Box>
              <Box>
                <Typography variant="body2" fontWeight="medium" sx={{ lineHeight: 1 }}>
                  Completamento
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  {statistiche.comande_create || 0} create
                </Typography>
              </Box>
            </Stack>
          </Stack>
        </Paper>
      )}

      {/* Sezione Responsabili - Elemento Principale */}
      <Box>
        <Box>
          {/* Toolbar Responsabili */}
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
            <Typography variant="h5" sx={{ fontWeight: 500, color: 'text.primary' }}>
              Responsabili del Cantiere
            </Typography>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={() => handleOpenResponsabileDialog('create')}
              sx={{
                textTransform: 'none',
                fontWeight: 500,
                px: 3,
                py: 1
              }}
            >
              Inserisci Responsabile
            </Button>
          </Box>

          {loadingResponsabili ? (
            <Box display="flex" justifyContent="center" py={4}>
              <CircularProgress />
            </Box>
          ) : (
            <Box>
              {responsabili.length === 0 ? (
                <Paper
                  elevation={0}
                  sx={{
                    p: 6,
                    textAlign: 'center',
                    backgroundColor: 'grey.50',
                    border: '1px dashed',
                    borderColor: 'grey.300'
                  }}
                >
                  <PersonIcon sx={{ fontSize: 48, color: 'grey.400', mb: 2 }} />
                  <Typography variant="h6" color="text.secondary" gutterBottom>
                    Nessun responsabile configurato
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                    Aggiungi il primo responsabile per iniziare a gestire le comande
                  </Typography>
                  <Button
                    variant="contained"
                    startIcon={<AddIcon />}
                    onClick={() => handleOpenResponsabileDialog('create')}
                    sx={{ textTransform: 'none' }}
                  >
                    Inserisci Primo Responsabile
                  </Button>
                </Paper>
              ) : (
                responsabili.map((responsabile) => (
                  <Accordion
                    key={responsabile.id_responsabile}
                    sx={{
                      mb: 2,
                      '&:before': { display: 'none' },
                      boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
                      border: '1px solid',
                      borderColor: 'grey.200'
                    }}
                  >
                    <AccordionSummary
                      expandIcon={<ExpandMoreIcon />}
                      sx={{
                        '&:hover': {
                          backgroundColor: 'grey.50'
                        }
                      }}
                    >
                      <Box display="flex" alignItems="center" justifyContent="space-between" width="100%">
                        <Box display="flex" alignItems="center" gap={2}>
                          <PersonIcon color="primary" sx={{ fontSize: 28 }} />
                          <Box>
                            <Typography variant="h6" sx={{ fontWeight: 500 }}>
                              {responsabile.nome_responsabile}
                            </Typography>
                            <Box display="flex" gap={3} mt={0.5}>
                              {responsabile.email && (
                                <Box display="flex" alignItems="center" gap={0.5}>
                                  <EmailIcon fontSize="small" color="action" />
                                  <Typography variant="body2" color="text.secondary">
                                    {responsabile.email}
                                  </Typography>
                                </Box>
                              )}
                              {responsabile.telefono && (
                                <Box display="flex" alignItems="center" gap={0.5}>
                                  <PhoneIcon fontSize="small" color="action" />
                                  <Typography variant="body2" color="text.secondary">
                                    {responsabile.telefono}
                                  </Typography>
                                </Box>
                              )}
                            </Box>
                          </Box>
                        </Box>

                        <Box display="flex" alignItems="center" gap={1} onClick={(e) => e.stopPropagation()}>
                          <Chip
                            icon={<AssignIcon />}
                            label={`${Array.isArray(comandePerResponsabile[responsabile.id_responsabile]) ? comandePerResponsabile[responsabile.id_responsabile].length : 0} comande`}
                            size="small"
                            color="primary"
                            variant="outlined"
                            clickable
                            onClick={() => {
                              // Pre-seleziona il responsabile nel dialog di creazione comanda
                              setOpenCreaConCavi(true);
                            }}
                            sx={{
                              fontWeight: 500,
                              '&:hover': {
                                backgroundColor: 'primary.light',
                                color: 'white'
                              }
                            }}
                          />
                          <Tooltip title="Modifica responsabile">
                            <IconButton
                              size="small"
                              onClick={() => handleOpenResponsabileDialog('edit', responsabile)}
                              sx={{
                                '&:hover': {
                                  backgroundColor: 'primary.light',
                                  color: 'white'
                                }
                              }}
                            >
                              <EditIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Elimina responsabile">
                            <IconButton
                              size="small"
                              onClick={() => handleDeleteResponsabile(responsabile.id_responsabile)}
                              sx={{
                                '&:hover': {
                                  backgroundColor: 'error.light',
                                  color: 'white'
                                }
                              }}
                            >
                              <DeleteIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        </Box>
                      </Box>
                    </AccordionSummary>

                    <AccordionDetails sx={{ pt: 2 }}>
                      <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 500, mb: 2 }}>
                        Comande Assegnate
                      </Typography>

                      {(!Array.isArray(comandePerResponsabile[responsabile.id_responsabile]) || comandePerResponsabile[responsabile.id_responsabile].length === 0) ? (
                        <Box
                          sx={{
                            p: 3,
                            textAlign: 'center',
                            backgroundColor: 'grey.50',
                            borderRadius: 1,
                            border: '1px dashed',
                            borderColor: 'grey.300'
                          }}
                        >
                          <Typography variant="body2" color="text.secondary">
                            Nessuna comanda assegnata a questo responsabile
                          </Typography>
                        </Box>
                      ) : (
                        <List dense>
                          {Array.isArray(comandePerResponsabile[responsabile.id_responsabile]) && comandePerResponsabile[responsabile.id_responsabile].map((comanda) => (
                            <ListItem
                              key={comanda.codice_comanda}
                              divider
                              sx={{
                                '&:hover': {
                                  backgroundColor: 'rgba(33, 150, 243, 0.1)'
                                }
                              }}
                            >
                              <ListItemText
                                primary={
                                  <Box display="flex" alignItems="center" gap={1}>
                                    <Typography variant="body2" fontWeight="bold">
                                      {comanda.codice_comanda}
                                    </Typography>
                                    <Chip
                                      label={getTipoComandaLabel(comanda.tipo_comanda)}
                                      size="small"
                                      variant="outlined"
                                    />
                                    <Chip
                                      label={comanda.stato || 'CREATA'}
                                      size="small"
                                      color={getStatoColor(comanda.stato)}
                                    />
                                    {comanda.priorita && comanda.priorita !== 'NORMALE' && (
                                      <Chip
                                        label={comanda.priorita}
                                        size="small"
                                        color={getPrioritaColor(comanda.priorita)}
                                        variant="filled"
                                      />
                                    )}
                                  </Box>
                                }
                                secondary={
                                  <Box>
                                    <Typography variant="body2" color="textSecondary">
                                      {comanda.descrizione || 'Nessuna descrizione'}
                                      {comanda.data_creazione && ` • Creata: ${new Date(comanda.data_creazione).toLocaleDateString()}`}
                                    </Typography>
                                    {comanda.numero_cavi_assegnati > 0 && (
                                      <Typography variant="caption" color="primary">
                                        {comanda.numero_cavi_assegnati} cavi assegnati
                                        {comanda.percentuale_completamento && ` • ${comanda.percentuale_completamento.toFixed(1)}% completato`}
                                      </Typography>
                                    )}
                                  </Box>
                                }
                              />
                              <Box display="flex" gap={0.5} ml={1}>
                                <Tooltip title="Visualizza">
                                  <IconButton
                                    size="small"
                                    onClick={() => handleOpenComandaDialog('view', comanda)}
                                    sx={{
                                      '&:hover': {
                                        backgroundColor: '#2196f3',
                                        color: 'white'
                                      }
                                    }}
                                  >
                                    <ViewIcon fontSize="small" />
                                  </IconButton>
                                </Tooltip>
                                <Tooltip title="Modifica">
                                  <IconButton
                                    size="small"
                                    onClick={() => handleOpenComandaDialog('edit', comanda)}
                                    sx={{
                                      '&:hover': {
                                        backgroundColor: '#1976d2',
                                        color: 'white'
                                      }
                                    }}
                                  >
                                    <EditIcon fontSize="small" />
                                  </IconButton>
                                </Tooltip>
                                <Tooltip title="Elimina">
                                  <IconButton
                                    size="small"
                                    onClick={() => handleDeleteComanda(comanda.codice_comanda)}
                                    sx={{
                                      '&:hover': {
                                        backgroundColor: 'error.main',
                                        color: 'white'
                                      }
                                    }}
                                  >
                                    <DeleteIcon fontSize="small" />
                                  </IconButton>
                                </Tooltip>
                              </Box>
                            </ListItem>
                          ))}
                        </List>
                      )}
                    </AccordionDetails>
                  </Accordion>
                ))
              )}
            </Box>
          )}
        </Box>
      </Box>

      {/* Dialog per creazione/modifica responsabile */}
      <Dialog
        open={openResponsabileDialog}
        onClose={handleCloseResponsabileDialog}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: { borderRadius: 2 }
        }}
      >
        <DialogTitle sx={{ pb: 1 }}>
          <Typography variant="h6" sx={{ fontWeight: 600 }}>
            {dialogModeResponsabile === 'create' ? 'Inserisci Responsabile' : 'Modifica Responsabile'}
          </Typography>
        </DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2 }}>
            <TextField
              fullWidth
              label="Nome Responsabile"
              value={formDataResponsabile.nome_responsabile}
              onChange={(e) => setFormDataResponsabile({ ...formDataResponsabile, nome_responsabile: e.target.value })}
              margin="normal"
              required
              variant="outlined"
              sx={{ mb: 2 }}
            />

            <TextField
              fullWidth
              label="Email"
              type="email"
              value={formDataResponsabile.email}
              onChange={(e) => setFormDataResponsabile({ ...formDataResponsabile, email: e.target.value })}
              margin="normal"
              variant="outlined"
              helperText="Email per notifiche (opzionale se inserisci telefono)"
              sx={{ mb: 2 }}
            />

            <TextField
              fullWidth
              label="Telefono"
              value={formDataResponsabile.telefono}
              onChange={(e) => setFormDataResponsabile({ ...formDataResponsabile, telefono: e.target.value })}
              margin="normal"
              variant="outlined"
              helperText="Numero per SMS (opzionale se inserisci email)"
            />
          </Box>
        </DialogContent>
        <DialogActions sx={{ p: 3, pt: 2 }}>
          <Button
            onClick={handleCloseResponsabileDialog}
            sx={{ textTransform: 'none' }}
          >
            Annulla
          </Button>
          <Button
            onClick={handleSubmitResponsabile}
            variant="contained"
            sx={{
              textTransform: 'none',
              fontWeight: 500,
              px: 3
            }}
          >
            {dialogModeResponsabile === 'create' ? 'Crea' : 'Salva'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Dialog per visualizzazione/modifica comanda */}
      <Dialog
        open={openComandaDialog}
        onClose={handleCloseComandaDialog}
        maxWidth="md"
        fullWidth
        PaperProps={{
          sx: { borderRadius: 2 }
        }}
      >
        <DialogTitle sx={{ pb: 1 }}>
          <Typography variant="h6" sx={{ fontWeight: 600 }}>
            {dialogModeComanda === 'view' ? 'Dettagli Comanda' : 'Modifica Comanda'}
          </Typography>
        </DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2 }}>
            {dialogModeComanda === 'view' && selectedComanda ? (
              <List>
                <ListItem>
                  <ListItemText
                    primary="Codice Comanda"
                    secondary={selectedComanda.codice_comanda}
                  />
                </ListItem>
                <Divider />
                <ListItem>
                  <ListItemText
                    primary="Tipo"
                    secondary={getTipoComandaLabel(selectedComanda.tipo_comanda)}
                  />
                </ListItem>
                <Divider />
                <ListItem>
                  <ListItemText
                    primary="Stato"
                    secondary={
                      <Chip
                        label={selectedComanda.stato}
                        color={getStatoColor(selectedComanda.stato)}
                        size="small"
                      />
                    }
                  />
                </ListItem>
                <Divider />
                <ListItem>
                  <ListItemText
                    primary="Descrizione"
                    secondary={selectedComanda.descrizione || 'Nessuna descrizione'}
                  />
                </ListItem>
                <Divider />
                <ListItem>
                  <ListItemText
                    primary="Priorità"
                    secondary={
                      <Chip
                        label={selectedComanda.priorita || 'NORMALE'}
                        color={getPrioritaColor(selectedComanda.priorita || 'NORMALE')}
                        size="small"
                      />
                    }
                  />
                </ListItem>
                <Divider />
                <ListItem>
                  <ListItemText
                    primary="Responsabile"
                    secondary={selectedComanda.responsabile || 'Non assegnato'}
                  />
                </ListItem>
                {selectedComanda.note_capo_cantiere && (
                  <>
                    <Divider />
                    <ListItem>
                      <ListItemText
                        primary="Note Capo Cantiere"
                        secondary={selectedComanda.note_capo_cantiere}
                      />
                    </ListItem>
                  </>
                )}
                <Divider />
                <ListItem>
                  <ListItemText
                    primary="Data Creazione"
                    secondary={new Date(selectedComanda.data_creazione).toLocaleDateString('it-IT')}
                  />
                </ListItem>
                {selectedComanda.data_scadenza && (
                  <>
                    <Divider />
                    <ListItem>
                      <ListItemText
                        primary="Data Scadenza"
                        secondary={new Date(selectedComanda.data_scadenza).toLocaleDateString('it-IT')}
                      />
                    </ListItem>
                  </>
                )}
                <Divider />
                <ListItem>
                  <ListItemText
                    primary="Cavi Assegnati"
                    secondary={selectedComanda.numero_cavi_assegnati || 0}
                  />
                </ListItem>
                <Divider />
                <ListItem>
                  <ListItemText
                    primary="Completamento"
                    secondary={`${(selectedComanda.percentuale_completamento || 0).toFixed(1)}%`}
                  />
                </ListItem>
              </List>
            ) : (
              <>
                <TextField
                  fullWidth
                  select
                  label="Tipo Comanda"
                  value={formDataComanda.tipo_comanda}
                  onChange={(e) => setFormDataComanda({ ...formDataComanda, tipo_comanda: e.target.value })}
                  margin="normal"
                  sx={{ mb: 2 }}
                >
                  <MenuItem value="POSA">Posa</MenuItem>
                  <MenuItem value="COLLEGAMENTO_PARTENZA">Collegamento Partenza</MenuItem>
                  <MenuItem value="COLLEGAMENTO_ARRIVO">Collegamento Arrivo</MenuItem>
                  <MenuItem value="CERTIFICAZIONE">Certificazione</MenuItem>
                  <MenuItem value="TESTING">Testing</MenuItem>
                </TextField>

                <TextField
                  fullWidth
                  select
                  label="Priorità"
                  value={formDataComanda.priorita}
                  onChange={(e) => setFormDataComanda({ ...formDataComanda, priorita: e.target.value })}
                  margin="normal"
                  sx={{ mb: 2 }}
                >
                  <MenuItem value="BASSA">Bassa</MenuItem>
                  <MenuItem value="NORMALE">Normale</MenuItem>
                  <MenuItem value="ALTA">Alta</MenuItem>
                  <MenuItem value="URGENTE">Urgente</MenuItem>
                </TextField>

                <TextField
                  fullWidth
                  label="Descrizione"
                  value={formDataComanda.descrizione}
                  onChange={(e) => setFormDataComanda({ ...formDataComanda, descrizione: e.target.value })}
                  margin="normal"
                  multiline
                  rows={3}
                  sx={{ mb: 2 }}
                />

                <TextField
                  fullWidth
                  label="Responsabile"
                  value={formDataComanda.responsabile}
                  onChange={(e) => setFormDataComanda({ ...formDataComanda, responsabile: e.target.value })}
                  margin="normal"
                  required
                  helperText="Chi eseguirà il lavoro (obbligatorio)"
                  sx={{ mb: 2 }}
                />

                <TextField
                  fullWidth
                  label="Note Capo Cantiere"
                  value={formDataComanda.note_capo_cantiere}
                  onChange={(e) => setFormDataComanda({ ...formDataComanda, note_capo_cantiere: e.target.value })}
                  margin="normal"
                  multiline
                  rows={2}
                  helperText="Istruzioni specifiche per il responsabile"
                  sx={{ mb: 2 }}
                />

                <TextField
                  fullWidth
                  label="Data Scadenza"
                  type="date"
                  value={formDataComanda.data_scadenza}
                  onChange={(e) => setFormDataComanda({ ...formDataComanda, data_scadenza: e.target.value })}
                  margin="normal"
                  InputLabelProps={{
                    shrink: true,
                  }}
                />
              </>
            )}
          </Box>
        </DialogContent>
        <DialogActions sx={{ p: 3, pt: 2 }}>
          <Button
            onClick={handleCloseComandaDialog}
            sx={{ textTransform: 'none' }}
          >
            {dialogModeComanda === 'view' ? 'Chiudi' : 'Annulla'}
          </Button>
          {dialogModeComanda === 'edit' && (
            <Button
              onClick={handleSubmitComanda}
              variant="contained"
              sx={{
                textTransform: 'none',
                fontWeight: 500,
                px: 3
              }}
            >
              Salva Modifiche
            </Button>
          )}
        </DialogActions>
      </Dialog>

      {/* Dialog CreaComandaConCavi */}
      <CreaComandaConCavi
        cantiereId={cantiereId}
        open={openCreaConCavi}
        onClose={() => setOpenCreaConCavi(false)}
        onSuccess={() => {
          loadComande();
          loadStatistiche();
          loadResponsabili();
          setOpenCreaConCavi(false);
        }}
      />
    </Box>
  );
};

export default ComandeListRivoluzionato;
