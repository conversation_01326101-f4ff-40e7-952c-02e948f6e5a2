#!/usr/bin/env python3
"""
Test semplificato del flusso delle comande
"""

import sys
import os
sys.path.append('.')
sys.path.append('./modules')

def test_comande_flow():
    """Test del flusso delle comande usando le API"""
    print("🧪 === TEST FLUSSO COMANDE ===")
    
    try:
        from modules.comande_new import crea_comanda_con_cavi
        
        # Parametri di test
        id_cantiere = 1
        responsabile = "Mario Rossi Test"
        cavi_test = ["CAVO001", "CAVO002"]  # Assumiamo che esistano
        
        print(f"📋 Test con cantiere {id_cantiere}, responsabile '{responsabile}'")
        print(f"📋 Cavi di test: {cavi_test}")
        
        # Test creazione comanda di posa
        print("\n🚀 Creazione comanda di posa...")
        codice_comanda = crea_comanda_con_cavi(
            id_cantiere=id_cantiere,
            tipo_comanda='POSA',
            descrizione='Test comanda posa - verifica stato In corso',
            responsabile=responsabile,
            lista_id_cavi=cavi_test,
            responsabile_email="<EMAIL>",
            responsabile_telefono="+39 123 456 789"
        )
        
        if codice_comanda:
            print(f"✅ Comanda creata con successo: {codice_comanda}")
            print("✅ Il sistema dovrebbe aver impostato i cavi come 'In corso'")
            print("✅ I cavi sono ora assegnati al responsabile")
            
            # Verifica cavi della comanda
            from modules.comande_new import ottieni_cavi_comanda
            cavi_comanda = ottieni_cavi_comanda(codice_comanda)
            print(f"✅ Cavi assegnati alla comanda: {len(cavi_comanda)}")
            
            for cavo in cavi_comanda:
                print(f"   - {cavo['id_cavo']}: stato = {cavo.get('stato_installazione', 'N/D')}")
            
            return True
        else:
            print("❌ Errore nella creazione della comanda")
            print("   Possibili cause:")
            print("   - Cavi non esistenti nel cantiere")
            print("   - Cavi già assegnati ad altre comande")
            print("   - Problemi di connessione al database")
            return False
            
    except Exception as e:
        print(f"❌ Errore durante il test: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_api_backend():
    """Test delle API backend"""
    print("\n🧪 === TEST API BACKEND ===")
    
    try:
        import requests
        
        # Test connessione backend
        print("🔍 Test connessione backend...")
        response = requests.get("http://localhost:8001/health", timeout=5)
        if response.status_code == 200:
            print("✅ Backend raggiungibile")
        else:
            print(f"⚠️ Backend risponde con status {response.status_code}")
            
        # Test API comande
        print("🔍 Test API comande...")
        response = requests.get("http://localhost:8001/comande/cantiere/1", timeout=5)
        if response.status_code == 200:
            comande = response.json()
            print(f"✅ API comande funzionante - trovate {len(comande)} comande")
            return True
        else:
            print(f"❌ API comande non funziona - status {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Backend non raggiungibile su http://localhost:8001")
        print("   Assicurati che il backend sia in esecuzione")
        return False
    except Exception as e:
        print(f"❌ Errore nel test API: {str(e)}")
        return False

def main():
    print("🚀 AVVIO TEST SISTEMA COMANDE")
    print("="*50)
    
    # Test 1: Flusso comande
    print("\n1️⃣ Test flusso creazione comande...")
    test1_ok = test_comande_flow()
    
    # Test 2: API Backend
    print("\n2️⃣ Test API backend...")
    test2_ok = test_api_backend()
    
    # Risultati
    print("\n" + "="*50)
    print("📋 RIEPILOGO TEST")
    print("="*50)
    print(f"✅ Flusso comande: {'PASS' if test1_ok else 'FAIL'}")
    print(f"✅ API backend: {'PASS' if test2_ok else 'FAIL'}")
    
    if test1_ok and test2_ok:
        print("\n🎉 TUTTI I TEST SUPERATI!")
        print("\n📝 VERIFICA MANUALE:")
        print("1. Apri il frontend su http://localhost:3001")
        print("2. Vai su 'Gestione Comande'")
        print("3. Crea una nuova comanda di POSA")
        print("4. Verifica che i cavi selezionati diventino 'In corso'")
        print("5. Verifica che i cavi siano visibili sotto il responsabile")
    else:
        print("\n❌ ALCUNI TEST FALLITI!")
        print("Verificare i problemi segnalati sopra.")
    
    return test1_ok and test2_ok

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
