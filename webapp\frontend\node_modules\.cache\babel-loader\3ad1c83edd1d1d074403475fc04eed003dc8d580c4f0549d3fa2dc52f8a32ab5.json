{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\comande\\\\ComandeListRivoluzionato.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Button, Paper, Chip, IconButton, Dialog, DialogTitle, DialogContent, DialogActions, TextField, Alert, CircularProgress, Tooltip, List, ListItem, ListItemText, Accordion, AccordionSummary, AccordionDetails, Stack, MenuItem, Divider } from '@mui/material';\nimport { Add as AddIcon, Edit as EditIcon, Delete as DeleteIcon, Assignment as AssignIcon, Person as PersonIcon, Email as EmailIcon, Phone as PhoneIcon, ExpandMore as ExpandMoreIcon, CheckCircle as CheckCircleIcon, Verified as VerifiedIcon, Visibility as ViewIcon } from '@mui/icons-material';\nimport comandeService from '../../services/comandeService';\nimport responsabiliService from '../../services/responsabiliService';\nimport CreaComandaConCavi from './CreaComandaConCavi';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ComandeListRivoluzionato = ({\n  cantiereId,\n  cantiereName\n}) => {\n  _s();\n  // Stati principali - Responsabili come elemento principale\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n\n  // Stati comande\n  const [statistiche, setStatistiche] = useState(null);\n  const [openCreaConCavi, setOpenCreaConCavi] = useState(false);\n\n  // Stati responsabili\n  const [responsabili, setResponsabili] = useState([]);\n  const [loadingResponsabili, setLoadingResponsabili] = useState(false);\n  const [comandePerResponsabile, setComandePerResponsabile] = useState({});\n  const [openResponsabileDialog, setOpenResponsabileDialog] = useState(false);\n  const [dialogModeResponsabile, setDialogModeResponsabile] = useState('create');\n  const [selectedResponsabile, setSelectedResponsabile] = useState(null);\n  const [formDataResponsabile, setFormDataResponsabile] = useState({\n    nome_responsabile: '',\n    email: '',\n    telefono: ''\n  });\n\n  // Stati per dialog comande\n  const [openComandaDialog, setOpenComandaDialog] = useState(false);\n  const [dialogModeComanda, setDialogModeComanda] = useState('view'); // 'view', 'edit'\n  const [selectedComanda, setSelectedComanda] = useState(null);\n  const [formDataComanda, setFormDataComanda] = useState({\n    tipo_comanda: 'POSA',\n    descrizione: '',\n    responsabile: '',\n    data_scadenza: '',\n    priorita: 'NORMALE',\n    note_capo_cantiere: ''\n  });\n  const loadComande = async () => {\n    try {\n      setLoading(true);\n      // Non serve più salvare le comande in uno stato separato\n      // Le comande vengono caricate per responsabile\n      setError(null);\n    } catch (err) {\n      console.error('Errore nel caricamento delle comande:', err);\n      setError('Errore nel caricamento delle comande');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const loadStatistiche = async () => {\n    try {\n      const stats = await comandeService.getStatisticheComande(cantiereId);\n      setStatistiche(stats);\n    } catch (err) {\n      console.error('Errore nel caricamento delle statistiche:', err);\n    }\n  };\n  const loadResponsabili = async () => {\n    try {\n      setLoadingResponsabili(true);\n      setError(null);\n      const data = await responsabiliService.getResponsabiliCantiere(cantiereId);\n      setResponsabili(data || []);\n      await loadComandePerResponsabili(data || []);\n    } catch (err) {\n      var _err$response, _err$response$data;\n      console.error('Errore nel caricamento dei responsabili:', err);\n      const errorMessage = ((_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.detail) || err.message || 'Errore nel caricamento dei responsabili';\n      setError(`Errore nel caricamento dei responsabili: ${errorMessage}`);\n    } finally {\n      setLoadingResponsabili(false);\n    }\n  };\n\n  // Carica dati al mount - Focus sui responsabili\n  useEffect(() => {\n    if (cantiereId) {\n      loadResponsabili();\n      loadComande();\n      loadStatistiche();\n    }\n  }, [cantiereId]);\n  const loadComandePerResponsabili = async responsabiliList => {\n    try {\n      const comandeMap = {};\n      for (const responsabile of responsabiliList) {\n        try {\n          const response = await comandeService.getComandeByResponsabile(cantiereId, responsabile.nome_responsabile);\n          // Assicurati che sia sempre un array\n          let comande = [];\n          if (response && Array.isArray(response)) {\n            comande = response;\n          } else if (response && response.comande && Array.isArray(response.comande)) {\n            comande = response.comande;\n          } else if (response && response.data && Array.isArray(response.data)) {\n            comande = response.data;\n          }\n          comandeMap[responsabile.id_responsabile] = comande;\n        } catch (err) {\n          console.error(`Errore nel caricamento comande per ${responsabile.nome_responsabile}:`, err);\n          comandeMap[responsabile.id_responsabile] = [];\n        }\n      }\n      setComandePerResponsabile(comandeMap);\n    } catch (err) {\n      console.error('Errore nel caricamento delle comande:', err);\n    }\n  };\n\n  // Gestione responsabili\n  const handleOpenResponsabileDialog = (mode, responsabile = null) => {\n    setDialogModeResponsabile(mode);\n    setSelectedResponsabile(responsabile);\n    if (mode === 'edit' && responsabile) {\n      setFormDataResponsabile({\n        nome_responsabile: responsabile.nome_responsabile || '',\n        email: responsabile.email || '',\n        telefono: responsabile.telefono || ''\n      });\n    } else {\n      setFormDataResponsabile({\n        nome_responsabile: '',\n        email: '',\n        telefono: ''\n      });\n    }\n    setOpenResponsabileDialog(true);\n  };\n  const handleCloseResponsabileDialog = () => {\n    setOpenResponsabileDialog(false);\n    setSelectedResponsabile(null);\n    setError(null);\n  };\n  const handleSubmitResponsabile = async () => {\n    try {\n      setError(null);\n      if (!formDataResponsabile.nome_responsabile.trim()) {\n        setError('Il nome del responsabile è obbligatorio');\n        return;\n      }\n      if (!formDataResponsabile.email && !formDataResponsabile.telefono) {\n        setError('Almeno uno tra email e telefono deve essere specificato');\n        return;\n      }\n      if (dialogModeResponsabile === 'create') {\n        await responsabiliService.createResponsabile(cantiereId, formDataResponsabile);\n      } else if (dialogModeResponsabile === 'edit') {\n        await responsabiliService.updateResponsabile(selectedResponsabile.id_responsabile, formDataResponsabile);\n      }\n      handleCloseResponsabileDialog();\n      await loadResponsabili();\n    } catch (err) {\n      console.error('Errore nel salvataggio:', err);\n      setError(err.detail || 'Errore nel salvataggio del responsabile');\n    }\n  };\n  const handleDeleteResponsabile = async idResponsabile => {\n    if (!window.confirm('Sei sicuro di voler eliminare questo responsabile?')) {\n      return;\n    }\n    try {\n      await responsabiliService.deleteResponsabile(idResponsabile);\n      await loadResponsabili();\n    } catch (err) {\n      console.error('Errore nell\\'eliminazione:', err);\n      setError('Errore nell\\'eliminazione del responsabile');\n    }\n  };\n\n  // Gestione comande\n  const handleOpenComandaDialog = (mode, comanda = null) => {\n    setDialogModeComanda(mode);\n    setSelectedComanda(comanda);\n    if (mode === 'edit' && comanda) {\n      setFormDataComanda({\n        tipo_comanda: comanda.tipo_comanda,\n        descrizione: comanda.descrizione || '',\n        responsabile: comanda.responsabile || '',\n        data_scadenza: comanda.data_scadenza || '',\n        priorita: comanda.priorita || 'NORMALE',\n        note_capo_cantiere: comanda.note_capo_cantiere || ''\n      });\n    }\n    setOpenComandaDialog(true);\n  };\n  const handleCloseComandaDialog = () => {\n    setOpenComandaDialog(false);\n    setSelectedComanda(null);\n    setFormDataComanda({\n      tipo_comanda: 'POSA',\n      descrizione: '',\n      responsabile: '',\n      data_scadenza: '',\n      priorita: 'NORMALE',\n      note_capo_cantiere: ''\n    });\n  };\n  const handleSubmitComanda = async () => {\n    try {\n      if (dialogModeComanda === 'edit') {\n        await comandeService.updateComanda(selectedComanda.codice_comanda, formDataComanda);\n        handleCloseComandaDialog();\n        await loadResponsabili(); // Ricarica per aggiornare le comande\n        await loadStatistiche();\n      }\n    } catch (err) {\n      console.error('Errore nel salvataggio:', err);\n      setError('Errore nel salvataggio della comanda');\n    }\n  };\n  const handleDeleteComanda = async codiceComanda => {\n    if (!window.confirm('Sei sicuro di voler eliminare questa comanda?')) {\n      return;\n    }\n    try {\n      await comandeService.deleteComanda(codiceComanda);\n      await loadResponsabili(); // Ricarica per aggiornare le comande\n      await loadStatistiche();\n    } catch (err) {\n      console.error('Errore nell\\'eliminazione:', err);\n      setError('Errore nell\\'eliminazione della comanda');\n    }\n  };\n  const getTipoComandaLabel = tipo => {\n    const labels = {\n      'POSA': 'Posa',\n      'COLLEGAMENTO_PARTENZA': 'Coll. Partenza',\n      'COLLEGAMENTO_ARRIVO': 'Coll. Arrivo',\n      'CERTIFICAZIONE': 'Certificazione',\n      'TESTING': 'Testing/Certificazione'\n    };\n    return labels[tipo] || tipo;\n  };\n  const getStatoColor = stato => {\n    const colors = {\n      'CREATA': 'default',\n      'ASSEGNATA': 'primary',\n      'IN_CORSO': 'warning',\n      'COMPLETATA': 'success',\n      'ANNULLATA': 'error'\n    };\n    return colors[stato] || 'default';\n  };\n  const getPrioritaColor = priorita => {\n    const colors = {\n      'BASSA': 'default',\n      'NORMALE': 'primary',\n      'ALTA': 'warning',\n      'URGENTE': 'error'\n    };\n    return colors[priorita] || 'default';\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      justifyContent: \"center\",\n      alignItems: \"center\",\n      minHeight: \"400px\",\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 321,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 320,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      p: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      mb: 3,\n      children: /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        sx: {\n          fontWeight: 600,\n          color: 'primary.main'\n        },\n        children: cantiereName\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 330,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 329,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"error\",\n      sx: {\n        mb: 2\n      },\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 336,\n      columnNumber: 9\n    }, this), statistiche && /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 2,\n        mb: 3,\n        bgcolor: 'grey.50'\n      },\n      children: /*#__PURE__*/_jsxDEV(Stack, {\n        direction: \"row\",\n        spacing: 4,\n        alignItems: \"center\",\n        justifyContent: \"space-between\",\n        flexWrap: \"wrap\",\n        children: [/*#__PURE__*/_jsxDEV(Stack, {\n          direction: \"row\",\n          alignItems: \"center\",\n          spacing: 1,\n          children: [/*#__PURE__*/_jsxDEV(PersonIcon, {\n            color: \"primary\",\n            fontSize: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 347,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              fontWeight: \"bold\",\n              sx: {\n                lineHeight: 1\n              },\n              children: statistiche.responsabili_attivi || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 349,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"text.secondary\",\n              children: \"Responsabili\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 352,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 348,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 346,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Stack, {\n          direction: \"row\",\n          alignItems: \"center\",\n          spacing: 1,\n          children: [/*#__PURE__*/_jsxDEV(AssignIcon, {\n            color: \"info\",\n            fontSize: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 360,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              fontWeight: \"bold\",\n              sx: {\n                lineHeight: 1\n              },\n              children: statistiche.totale_comande || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 362,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"text.secondary\",\n              children: \"Totale\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 365,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 361,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 359,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Stack, {\n          direction: \"row\",\n          alignItems: \"center\",\n          spacing: 1,\n          children: [/*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n            color: \"warning\",\n            fontSize: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 373,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              fontWeight: \"bold\",\n              sx: {\n                lineHeight: 1\n              },\n              children: statistiche.comande_in_corso || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 375,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"text.secondary\",\n              children: \"In Corso\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 378,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 374,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 372,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Stack, {\n          direction: \"row\",\n          alignItems: \"center\",\n          spacing: 1,\n          children: [/*#__PURE__*/_jsxDEV(VerifiedIcon, {\n            color: \"success\",\n            fontSize: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 386,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              fontWeight: \"bold\",\n              sx: {\n                lineHeight: 1\n              },\n              children: statistiche.comande_completate || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 388,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"text.secondary\",\n              children: \"Completate\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 391,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 387,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 385,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Stack, {\n          direction: \"row\",\n          alignItems: \"center\",\n          spacing: 1,\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              width: 32,\n              height: 32,\n              borderRadius: '50%',\n              bgcolor: statistiche.comande_completate / (statistiche.totale_comande || 1) >= 0.8 ? 'success.main' : statistiche.comande_completate / (statistiche.totale_comande || 1) >= 0.5 ? 'warning.main' : 'error.main',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center'\n            },\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              fontWeight: \"bold\",\n              color: \"white\",\n              children: [statistiche.totale_comande > 0 ? Math.round(statistiche.comande_completate / statistiche.totale_comande * 100) : 0, \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 409,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 399,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              fontWeight: \"medium\",\n              sx: {\n                lineHeight: 1\n              },\n              children: \"Completamento\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 414,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"text.secondary\",\n              children: [statistiche.comande_create || 0, \" create\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 417,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 413,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 398,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 344,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 343,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          justifyContent: \"space-between\",\n          alignItems: \"center\",\n          mb: 3,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h5\",\n            sx: {\n              fontWeight: 500,\n              color: 'text.primary'\n            },\n            children: \"Responsabili del Cantiere\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 431,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 436,\n              columnNumber: 26\n            }, this),\n            onClick: () => handleOpenResponsabileDialog('create'),\n            sx: {\n              textTransform: 'none',\n              fontWeight: 500,\n              px: 3,\n              py: 1,\n              backgroundColor: '#f8f9fa',\n              color: '#212529',\n              border: '1px solid #dee2e6',\n              boxShadow: 'none',\n              '&:hover': {\n                backgroundColor: '#e9ecef',\n                boxShadow: 'none'\n              }\n            },\n            children: \"Inserisci Responsabile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 434,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 430,\n          columnNumber: 11\n        }, this), loadingResponsabili ? /*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          justifyContent: \"center\",\n          py: 4,\n          children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 459,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 458,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(Box, {\n          children: responsabili.length === 0 ? /*#__PURE__*/_jsxDEV(Paper, {\n            elevation: 0,\n            sx: {\n              p: 6,\n              textAlign: 'center',\n              backgroundColor: 'grey.50',\n              border: '1px dashed',\n              borderColor: 'grey.300'\n            },\n            children: [/*#__PURE__*/_jsxDEV(PersonIcon, {\n              sx: {\n                fontSize: 48,\n                color: 'grey.400',\n                mb: 2\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 474,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              color: \"text.secondary\",\n              gutterBottom: true,\n              children: \"Nessun responsabile configurato\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 475,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              sx: {\n                mb: 3\n              },\n              children: \"Aggiungi il primo responsabile per iniziare a gestire le comande\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 478,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 483,\n                columnNumber: 32\n              }, this),\n              onClick: () => handleOpenResponsabileDialog('create'),\n              sx: {\n                textTransform: 'none',\n                backgroundColor: '#f8f9fa',\n                color: '#212529',\n                border: '1px solid #dee2e6',\n                boxShadow: 'none',\n                '&:hover': {\n                  backgroundColor: '#e9ecef',\n                  boxShadow: 'none'\n                }\n              },\n              children: \"Inserisci Primo Responsabile\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 481,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 464,\n            columnNumber: 17\n          }, this) : responsabili.map(responsabile => /*#__PURE__*/_jsxDEV(Accordion, {\n            sx: {\n              mb: 2,\n              '&:before': {\n                display: 'none'\n              },\n              boxShadow: '0 1px 3px rgba(0,0,0,0.1)',\n              border: '1px solid',\n              borderColor: 'grey.200'\n            },\n            children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n              expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 513,\n                columnNumber: 35\n              }, this),\n              sx: {\n                '&:hover': {\n                  backgroundColor: 'grey.50'\n                }\n              },\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                alignItems: \"center\",\n                justifyContent: \"space-between\",\n                width: \"100%\",\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  display: \"flex\",\n                  alignItems: \"center\",\n                  gap: 2,\n                  children: [/*#__PURE__*/_jsxDEV(PersonIcon, {\n                    color: \"primary\",\n                    sx: {\n                      fontSize: 28\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 522,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"h6\",\n                      sx: {\n                        fontWeight: 500\n                      },\n                      children: responsabile.nome_responsabile\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 524,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      display: \"flex\",\n                      gap: 3,\n                      mt: 0.5,\n                      children: [responsabile.email && /*#__PURE__*/_jsxDEV(Box, {\n                        display: \"flex\",\n                        alignItems: \"center\",\n                        gap: 0.5,\n                        children: [/*#__PURE__*/_jsxDEV(EmailIcon, {\n                          fontSize: \"small\",\n                          color: \"action\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 530,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"body2\",\n                          color: \"text.secondary\",\n                          children: responsabile.email\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 531,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 529,\n                        columnNumber: 33\n                      }, this), responsabile.telefono && /*#__PURE__*/_jsxDEV(Box, {\n                        display: \"flex\",\n                        alignItems: \"center\",\n                        gap: 0.5,\n                        children: [/*#__PURE__*/_jsxDEV(PhoneIcon, {\n                          fontSize: \"small\",\n                          color: \"action\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 538,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"body2\",\n                          color: \"text.secondary\",\n                          children: responsabile.telefono\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 539,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 537,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 527,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 523,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 521,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  display: \"flex\",\n                  alignItems: \"center\",\n                  gap: 1,\n                  onClick: e => e.stopPropagation(),\n                  children: [/*#__PURE__*/_jsxDEV(Chip, {\n                    icon: /*#__PURE__*/_jsxDEV(AssignIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 550,\n                      columnNumber: 35\n                    }, this),\n                    label: `${Array.isArray(comandePerResponsabile[responsabile.id_responsabile]) ? comandePerResponsabile[responsabile.id_responsabile].length : 0} comande`,\n                    size: \"small\",\n                    color: \"primary\",\n                    variant: \"outlined\",\n                    clickable: true,\n                    onClick: () => {\n                      // Pre-seleziona il responsabile nel dialog di creazione comanda\n                      setOpenCreaConCavi(true);\n                    },\n                    sx: {\n                      fontWeight: 500,\n                      backgroundColor: 'rgba(33, 150, 243, 0.1)',\n                      color: '#1976d2',\n                      border: '1px solid rgba(33, 150, 243, 0.3)',\n                      '&:hover': {\n                        backgroundColor: 'rgba(33, 150, 243, 0.2) !important',\n                        color: '#1565c0 !important'\n                      }\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 549,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                    title: \"Modifica responsabile\",\n                    children: /*#__PURE__*/_jsxDEV(IconButton, {\n                      size: \"small\",\n                      onClick: () => handleOpenResponsabileDialog('edit', responsabile),\n                      sx: {\n                        '&:hover': {\n                          backgroundColor: 'primary.light',\n                          color: 'white'\n                        }\n                      },\n                      children: /*#__PURE__*/_jsxDEV(EditIcon, {\n                        fontSize: \"small\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 582,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 572,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 571,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                    title: \"Elimina responsabile\",\n                    children: /*#__PURE__*/_jsxDEV(IconButton, {\n                      size: \"small\",\n                      onClick: () => handleDeleteResponsabile(responsabile.id_responsabile),\n                      sx: {\n                        '&:hover': {\n                          backgroundColor: 'error.light',\n                          color: 'white'\n                        }\n                      },\n                      children: /*#__PURE__*/_jsxDEV(DeleteIcon, {\n                        fontSize: \"small\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 596,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 586,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 585,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 548,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 520,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 512,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n              sx: {\n                pt: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle2\",\n                gutterBottom: true,\n                sx: {\n                  fontWeight: 500,\n                  mb: 2\n                },\n                children: \"Comande Assegnate\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 604,\n                columnNumber: 23\n              }, this), !Array.isArray(comandePerResponsabile[responsabile.id_responsabile]) || comandePerResponsabile[responsabile.id_responsabile].length === 0 ? /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  p: 3,\n                  textAlign: 'center',\n                  backgroundColor: 'grey.50',\n                  borderRadius: 1,\n                  border: '1px dashed',\n                  borderColor: 'grey.300'\n                },\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: \"Nessuna comanda assegnata a questo responsabile\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 619,\n                  columnNumber: 27\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 609,\n                columnNumber: 25\n              }, this) : /*#__PURE__*/_jsxDEV(List, {\n                dense: true,\n                children: Array.isArray(comandePerResponsabile[responsabile.id_responsabile]) && comandePerResponsabile[responsabile.id_responsabile].map(comanda => /*#__PURE__*/_jsxDEV(ListItem, {\n                  divider: true,\n                  sx: {\n                    '&:hover': {\n                      backgroundColor: 'rgba(33, 150, 243, 0.1)'\n                    }\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(ListItemText, {\n                    primary: /*#__PURE__*/_jsxDEV(Box, {\n                      display: \"flex\",\n                      alignItems: \"center\",\n                      gap: 1,\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        fontWeight: \"bold\",\n                        children: comanda.codice_comanda\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 638,\n                        columnNumber: 37\n                      }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                        label: getTipoComandaLabel(comanda.tipo_comanda),\n                        size: \"small\",\n                        variant: \"outlined\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 641,\n                        columnNumber: 37\n                      }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                        label: comanda.stato || 'CREATA',\n                        size: \"small\",\n                        color: getStatoColor(comanda.stato)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 646,\n                        columnNumber: 37\n                      }, this), comanda.priorita && comanda.priorita !== 'NORMALE' && /*#__PURE__*/_jsxDEV(Chip, {\n                        label: comanda.priorita,\n                        size: \"small\",\n                        color: getPrioritaColor(comanda.priorita),\n                        variant: \"filled\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 652,\n                        columnNumber: 39\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 637,\n                      columnNumber: 35\n                    }, this),\n                    secondary: /*#__PURE__*/_jsxDEV(Box, {\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        color: \"textSecondary\",\n                        children: [comanda.descrizione || 'Nessuna descrizione', comanda.data_creazione && ` • Creata: ${new Date(comanda.data_creazione).toLocaleDateString()}`]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 663,\n                        columnNumber: 37\n                      }, this), comanda.numero_cavi_assegnati > 0 && /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        color: \"primary\",\n                        children: [comanda.numero_cavi_assegnati, \" cavi assegnati\", comanda.percentuale_completamento && ` • ${comanda.percentuale_completamento.toFixed(1)}% completato`]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 668,\n                        columnNumber: 39\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 662,\n                      columnNumber: 35\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 635,\n                    columnNumber: 31\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    display: \"flex\",\n                    gap: 0.5,\n                    ml: 1,\n                    children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n                      title: \"Visualizza\",\n                      children: /*#__PURE__*/_jsxDEV(IconButton, {\n                        size: \"small\",\n                        onClick: () => handleOpenComandaDialog('view', comanda),\n                        sx: {\n                          '&:hover': {\n                            backgroundColor: '#2196f3',\n                            color: 'white'\n                          }\n                        },\n                        children: /*#__PURE__*/_jsxDEV(ViewIcon, {\n                          fontSize: \"small\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 688,\n                          columnNumber: 37\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 678,\n                        columnNumber: 35\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 677,\n                      columnNumber: 33\n                    }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                      title: \"Modifica\",\n                      children: /*#__PURE__*/_jsxDEV(IconButton, {\n                        size: \"small\",\n                        onClick: () => handleOpenComandaDialog('edit', comanda),\n                        sx: {\n                          '&:hover': {\n                            backgroundColor: '#1976d2',\n                            color: 'white'\n                          }\n                        },\n                        children: /*#__PURE__*/_jsxDEV(EditIcon, {\n                          fontSize: \"small\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 702,\n                          columnNumber: 37\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 692,\n                        columnNumber: 35\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 691,\n                      columnNumber: 33\n                    }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                      title: \"Elimina\",\n                      children: /*#__PURE__*/_jsxDEV(IconButton, {\n                        size: \"small\",\n                        onClick: () => handleDeleteComanda(comanda.codice_comanda),\n                        sx: {\n                          '&:hover': {\n                            backgroundColor: 'error.main',\n                            color: 'white'\n                          }\n                        },\n                        children: /*#__PURE__*/_jsxDEV(DeleteIcon, {\n                          fontSize: \"small\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 716,\n                          columnNumber: 37\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 706,\n                        columnNumber: 35\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 705,\n                      columnNumber: 33\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 676,\n                    columnNumber: 31\n                  }, this)]\n                }, comanda.codice_comanda, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 626,\n                  columnNumber: 29\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 624,\n                columnNumber: 25\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 603,\n              columnNumber: 21\n            }, this)]\n          }, responsabile.id_responsabile, true, {\n            fileName: _jsxFileName,\n            lineNumber: 502,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 462,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 428,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 427,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: openResponsabileDialog,\n      onClose: handleCloseResponsabileDialog,\n      maxWidth: \"sm\",\n      fullWidth: true,\n      PaperProps: {\n        sx: {\n          borderRadius: 2\n        }\n      },\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        sx: {\n          pb: 1\n        },\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            fontWeight: 600\n          },\n          children: dialogModeResponsabile === 'create' ? 'Inserisci Responsabile' : 'Modifica Responsabile'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 744,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 743,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            pt: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Nome Responsabile\",\n            value: formDataResponsabile.nome_responsabile,\n            onChange: e => setFormDataResponsabile({\n              ...formDataResponsabile,\n              nome_responsabile: e.target.value\n            }),\n            margin: \"normal\",\n            required: true,\n            variant: \"outlined\",\n            sx: {\n              mb: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 750,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Email\",\n            type: \"email\",\n            value: formDataResponsabile.email,\n            onChange: e => setFormDataResponsabile({\n              ...formDataResponsabile,\n              email: e.target.value\n            }),\n            margin: \"normal\",\n            variant: \"outlined\",\n            helperText: \"Email per notifiche (opzionale se inserisci telefono)\",\n            sx: {\n              mb: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 761,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Telefono\",\n            value: formDataResponsabile.telefono,\n            onChange: e => setFormDataResponsabile({\n              ...formDataResponsabile,\n              telefono: e.target.value\n            }),\n            margin: \"normal\",\n            variant: \"outlined\",\n            helperText: \"Numero per SMS (opzionale se inserisci email)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 773,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 749,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 748,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        sx: {\n          p: 3,\n          pt: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCloseResponsabileDialog,\n          sx: {\n            textTransform: 'none'\n          },\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 785,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleSubmitResponsabile,\n          variant: \"contained\",\n          sx: {\n            textTransform: 'none',\n            fontWeight: 500,\n            px: 3\n          },\n          children: dialogModeResponsabile === 'create' ? 'Crea' : 'Salva'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 791,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 784,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 734,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: openComandaDialog,\n      onClose: handleCloseComandaDialog,\n      maxWidth: \"md\",\n      fullWidth: true,\n      PaperProps: {\n        sx: {\n          borderRadius: 2\n        }\n      },\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        sx: {\n          pb: 1\n        },\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            fontWeight: 600\n          },\n          children: dialogModeComanda === 'view' ? 'Dettagli Comanda' : 'Modifica Comanda'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 816,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 815,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            pt: 2\n          },\n          children: dialogModeComanda === 'view' && selectedComanda ? /*#__PURE__*/_jsxDEV(List, {\n            children: [/*#__PURE__*/_jsxDEV(ListItem, {\n              children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: \"Codice Comanda\",\n                secondary: selectedComanda.codice_comanda\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 825,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 824,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 830,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n              children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: \"Tipo\",\n                secondary: getTipoComandaLabel(selectedComanda.tipo_comanda)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 832,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 831,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 837,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n              children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: \"Stato\",\n                secondary: /*#__PURE__*/_jsxDEV(Chip, {\n                  label: selectedComanda.stato,\n                  color: getStatoColor(selectedComanda.stato),\n                  size: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 842,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 839,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 838,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 850,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n              children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: \"Descrizione\",\n                secondary: selectedComanda.descrizione || 'Nessuna descrizione'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 852,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 851,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 857,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n              children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: \"Priorit\\xE0\",\n                secondary: /*#__PURE__*/_jsxDEV(Chip, {\n                  label: selectedComanda.priorita || 'NORMALE',\n                  color: getPrioritaColor(selectedComanda.priorita || 'NORMALE'),\n                  size: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 862,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 859,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 858,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 870,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n              children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: \"Responsabile\",\n                secondary: selectedComanda.responsabile || 'Non assegnato'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 872,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 871,\n              columnNumber: 17\n            }, this), selectedComanda.note_capo_cantiere && /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 879,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Note Capo Cantiere\",\n                  secondary: selectedComanda.note_capo_cantiere\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 881,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 880,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 888,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n              children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: \"Data Creazione\",\n                secondary: new Date(selectedComanda.data_creazione).toLocaleDateString('it-IT')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 890,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 889,\n              columnNumber: 17\n            }, this), selectedComanda.data_scadenza && /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 897,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Data Scadenza\",\n                  secondary: new Date(selectedComanda.data_scadenza).toLocaleDateString('it-IT')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 899,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 898,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 906,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n              children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: \"Cavi Assegnati\",\n                secondary: selectedComanda.numero_cavi_assegnati || 0\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 908,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 907,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 913,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n              children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: \"Completamento\",\n                secondary: `${(selectedComanda.percentuale_completamento || 0).toFixed(1)}%`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 915,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 914,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 823,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              select: true,\n              label: \"Tipo Comanda\",\n              value: formDataComanda.tipo_comanda,\n              onChange: e => setFormDataComanda({\n                ...formDataComanda,\n                tipo_comanda: e.target.value\n              }),\n              margin: \"normal\",\n              sx: {\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"POSA\",\n                children: \"Posa\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 932,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"COLLEGAMENTO_PARTENZA\",\n                children: \"Collegamento Partenza\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 933,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"COLLEGAMENTO_ARRIVO\",\n                children: \"Collegamento Arrivo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 934,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"CERTIFICAZIONE\",\n                children: \"Certificazione\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 935,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"TESTING\",\n                children: \"Testing\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 936,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 923,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              select: true,\n              label: \"Priorit\\xE0\",\n              value: formDataComanda.priorita,\n              onChange: e => setFormDataComanda({\n                ...formDataComanda,\n                priorita: e.target.value\n              }),\n              margin: \"normal\",\n              sx: {\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"BASSA\",\n                children: \"Bassa\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 948,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"NORMALE\",\n                children: \"Normale\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 949,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"ALTA\",\n                children: \"Alta\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 950,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"URGENTE\",\n                children: \"Urgente\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 951,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 939,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Descrizione\",\n              value: formDataComanda.descrizione,\n              onChange: e => setFormDataComanda({\n                ...formDataComanda,\n                descrizione: e.target.value\n              }),\n              margin: \"normal\",\n              multiline: true,\n              rows: 3,\n              sx: {\n                mb: 2\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 954,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Responsabile\",\n              value: formDataComanda.responsabile,\n              onChange: e => setFormDataComanda({\n                ...formDataComanda,\n                responsabile: e.target.value\n              }),\n              margin: \"normal\",\n              required: true,\n              helperText: \"Chi eseguir\\xE0 il lavoro (obbligatorio)\",\n              sx: {\n                mb: 2\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 965,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Note Capo Cantiere\",\n              value: formDataComanda.note_capo_cantiere,\n              onChange: e => setFormDataComanda({\n                ...formDataComanda,\n                note_capo_cantiere: e.target.value\n              }),\n              margin: \"normal\",\n              multiline: true,\n              rows: 2,\n              helperText: \"Istruzioni specifiche per il responsabile\",\n              sx: {\n                mb: 2\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 976,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Data Scadenza\",\n              type: \"date\",\n              value: formDataComanda.data_scadenza,\n              onChange: e => setFormDataComanda({\n                ...formDataComanda,\n                data_scadenza: e.target.value\n              }),\n              margin: \"normal\",\n              InputLabelProps: {\n                shrink: true\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 988,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 821,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 820,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        sx: {\n          p: 3,\n          pt: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCloseComandaDialog,\n          sx: {\n            textTransform: 'none'\n          },\n          children: dialogModeComanda === 'view' ? 'Chiudi' : 'Annulla'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1004,\n          columnNumber: 11\n        }, this), dialogModeComanda === 'edit' && /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleSubmitComanda,\n          variant: \"contained\",\n          sx: {\n            textTransform: 'none',\n            fontWeight: 500,\n            px: 3\n          },\n          children: \"Salva Modifiche\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1011,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1003,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 806,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(CreaComandaConCavi, {\n      cantiereId: cantiereId,\n      open: openCreaConCavi,\n      onClose: () => setOpenCreaConCavi(false),\n      onSuccess: () => {\n        loadComande();\n        loadStatistiche();\n        loadResponsabili();\n        setOpenCreaConCavi(false);\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1027,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 327,\n    columnNumber: 5\n  }, this);\n};\n_s(ComandeListRivoluzionato, \"VO5gnoC6vfaaVGr2JtzCushM5jE=\");\n_c = ComandeListRivoluzionato;\nexport default ComandeListRivoluzionato;\nvar _c;\n$RefreshReg$(_c, \"ComandeListRivoluzionato\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "<PERSON><PERSON>", "Paper", "Chip", "IconButton", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "TextField", "<PERSON><PERSON>", "CircularProgress", "<PERSON><PERSON><PERSON>", "List", "ListItem", "ListItemText", "Accordion", "AccordionSummary", "AccordionDetails", "<PERSON><PERSON>", "MenuItem", "Divider", "Add", "AddIcon", "Edit", "EditIcon", "Delete", "DeleteIcon", "Assignment", "AssignIcon", "Person", "PersonIcon", "Email", "EmailIcon", "Phone", "PhoneIcon", "ExpandMore", "ExpandMoreIcon", "CheckCircle", "CheckCircleIcon", "Verified", "VerifiedIcon", "Visibility", "ViewIcon", "comandeService", "responsabiliService", "CreaComandaConCavi", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ComandeListRivoluzionato", "cantiereId", "cantiereName", "_s", "loading", "setLoading", "error", "setError", "statistiche", "setStatistiche", "openCreaConCavi", "setOpenCreaConCavi", "responsabili", "setResponsabili", "loadingResponsabili", "setLoadingResponsabili", "comandePerResponsabile", "setComandePerResponsabile", "openResponsabileDialog", "setOpenResponsabileDialog", "dialogModeResponsabile", "setDialogModeResponsabile", "selectedResponsabile", "setSelectedResponsabile", "formDataResponsabile", "setFormDataResponsabile", "nome_responsabile", "email", "telefono", "openComandaDialog", "setOpenComandaDialog", "dialogModeComanda", "setDialogModeComanda", "selectedComanda", "setSelectedComanda", "formDataComanda", "setFormDataComanda", "tipo_comanda", "descrizione", "responsabile", "data_scadenza", "priorita", "note_capo_cantiere", "loadComande", "err", "console", "loadStatistiche", "stats", "getStatisticheComande", "loadResponsabili", "data", "getResponsabiliCantiere", "loadComandePerResponsabili", "_err$response", "_err$response$data", "errorMessage", "response", "detail", "message", "responsabiliList", "comandeMap", "getComandeByResponsabile", "comande", "Array", "isArray", "id_responsabile", "handleOpenResponsabileDialog", "mode", "handleCloseResponsabileDialog", "handleSubmitResponsabile", "trim", "createResponsabile", "updateResponsabile", "handleDeleteResponsabile", "idResponsabile", "window", "confirm", "deleteResponsabile", "handleOpenComandaDialog", "comanda", "handleCloseComandaDialog", "handleSubmitComanda", "updateComanda", "codice_comanda", "handleDeleteComanda", "codiceComanda", "deleteComanda", "getTipoComandaLabel", "tipo", "labels", "getStatoColor", "stato", "colors", "getPrioritaColor", "display", "justifyContent", "alignItems", "minHeight", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sx", "p", "mb", "variant", "fontWeight", "color", "severity", "bgcolor", "direction", "spacing", "flexWrap", "fontSize", "lineHeight", "responsabili_attivi", "totale_comande", "comande_in_corso", "comande_completate", "width", "height", "borderRadius", "Math", "round", "comande_create", "startIcon", "onClick", "textTransform", "px", "py", "backgroundColor", "border", "boxShadow", "length", "elevation", "textAlign", "borderColor", "gutterBottom", "map", "expandIcon", "gap", "mt", "e", "stopPropagation", "icon", "label", "size", "clickable", "title", "pt", "dense", "divider", "primary", "secondary", "data_creazione", "Date", "toLocaleDateString", "numero_cavi_assegnati", "percentuale_completamento", "toFixed", "ml", "open", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "PaperProps", "pb", "value", "onChange", "target", "margin", "required", "type", "helperText", "select", "multiline", "rows", "InputLabelProps", "shrink", "onSuccess", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/comande/ComandeListRivoluzionato.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  Button,\n  Paper,\n  Chip,\n  IconButton,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  TextField,\n  Alert,\n  CircularProgress,\n  Tooltip,\n  List,\n  ListItem,\n  ListItemText,\n  Accordion,\n  AccordionSummary,\n  AccordionDetails,\n  Stack,\n  MenuItem,\n  Divider\n} from '@mui/material';\nimport {\n  Add as AddIcon,\n  Edit as EditIcon,\n  Delete as DeleteIcon,\n  Assignment as AssignIcon,\n  Person as PersonIcon,\n  Email as EmailIcon,\n  Phone as PhoneIcon,\n  ExpandMore as ExpandMoreIcon,\n  CheckCircle as CheckCircleIcon,\n  Verified as VerifiedIcon,\n  Visibility as ViewIcon\n} from '@mui/icons-material';\nimport comandeService from '../../services/comandeService';\nimport responsabiliService from '../../services/responsabiliService';\nimport CreaComandaConCavi from './CreaComandaConCavi';\n\nconst ComandeListRivoluzionato = ({ cantiereId, cantiereName }) => {\n  // Stati principali - Responsabili come elemento principale\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n\n  // Stati comande\n  const [statistiche, setStatistiche] = useState(null);\n  const [openCreaConCavi, setOpenCreaConCavi] = useState(false);\n\n  // Stati responsabili\n  const [responsabili, setResponsabili] = useState([]);\n  const [loadingResponsabili, setLoadingResponsabili] = useState(false);\n  const [comandePerResponsabile, setComandePerResponsabile] = useState({});\n  const [openResponsabileDialog, setOpenResponsabileDialog] = useState(false);\n  const [dialogModeResponsabile, setDialogModeResponsabile] = useState('create');\n  const [selectedResponsabile, setSelectedResponsabile] = useState(null);\n  const [formDataResponsabile, setFormDataResponsabile] = useState({\n    nome_responsabile: '',\n    email: '',\n    telefono: ''\n  });\n\n  // Stati per dialog comande\n  const [openComandaDialog, setOpenComandaDialog] = useState(false);\n  const [dialogModeComanda, setDialogModeComanda] = useState('view'); // 'view', 'edit'\n  const [selectedComanda, setSelectedComanda] = useState(null);\n  const [formDataComanda, setFormDataComanda] = useState({\n    tipo_comanda: 'POSA',\n    descrizione: '',\n    responsabile: '',\n    data_scadenza: '',\n    priorita: 'NORMALE',\n    note_capo_cantiere: ''\n  });\n\n  const loadComande = async () => {\n    try {\n      setLoading(true);\n      // Non serve più salvare le comande in uno stato separato\n      // Le comande vengono caricate per responsabile\n      setError(null);\n    } catch (err) {\n      console.error('Errore nel caricamento delle comande:', err);\n      setError('Errore nel caricamento delle comande');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const loadStatistiche = async () => {\n    try {\n      const stats = await comandeService.getStatisticheComande(cantiereId);\n      setStatistiche(stats);\n    } catch (err) {\n      console.error('Errore nel caricamento delle statistiche:', err);\n    }\n  };\n\n  const loadResponsabili = async () => {\n    try {\n      setLoadingResponsabili(true);\n      setError(null);\n\n      const data = await responsabiliService.getResponsabiliCantiere(cantiereId);\n      setResponsabili(data || []);\n      await loadComandePerResponsabili(data || []);\n    } catch (err) {\n      console.error('Errore nel caricamento dei responsabili:', err);\n      const errorMessage = err.response?.data?.detail || err.message || 'Errore nel caricamento dei responsabili';\n      setError(`Errore nel caricamento dei responsabili: ${errorMessage}`);\n    } finally {\n      setLoadingResponsabili(false);\n    }\n  };\n\n  // Carica dati al mount - Focus sui responsabili\n  useEffect(() => {\n    if (cantiereId) {\n      loadResponsabili();\n      loadComande();\n      loadStatistiche();\n    }\n  }, [cantiereId]);\n\n  const loadComandePerResponsabili = async (responsabiliList) => {\n    try {\n      const comandeMap = {};\n      for (const responsabile of responsabiliList) {\n        try {\n          const response = await comandeService.getComandeByResponsabile(cantiereId, responsabile.nome_responsabile);\n          // Assicurati che sia sempre un array\n          let comande = [];\n          if (response && Array.isArray(response)) {\n            comande = response;\n          } else if (response && response.comande && Array.isArray(response.comande)) {\n            comande = response.comande;\n          } else if (response && response.data && Array.isArray(response.data)) {\n            comande = response.data;\n          }\n          comandeMap[responsabile.id_responsabile] = comande;\n        } catch (err) {\n          console.error(`Errore nel caricamento comande per ${responsabile.nome_responsabile}:`, err);\n          comandeMap[responsabile.id_responsabile] = [];\n        }\n      }\n      setComandePerResponsabile(comandeMap);\n    } catch (err) {\n      console.error('Errore nel caricamento delle comande:', err);\n    }\n  };\n\n  // Gestione responsabili\n  const handleOpenResponsabileDialog = (mode, responsabile = null) => {\n    setDialogModeResponsabile(mode);\n    setSelectedResponsabile(responsabile);\n    \n    if (mode === 'edit' && responsabile) {\n      setFormDataResponsabile({\n        nome_responsabile: responsabile.nome_responsabile || '',\n        email: responsabile.email || '',\n        telefono: responsabile.telefono || ''\n      });\n    } else {\n      setFormDataResponsabile({\n        nome_responsabile: '',\n        email: '',\n        telefono: ''\n      });\n    }\n    \n    setOpenResponsabileDialog(true);\n  };\n\n  const handleCloseResponsabileDialog = () => {\n    setOpenResponsabileDialog(false);\n    setSelectedResponsabile(null);\n    setError(null);\n  };\n\n  const handleSubmitResponsabile = async () => {\n    try {\n      setError(null);\n      \n      if (!formDataResponsabile.nome_responsabile.trim()) {\n        setError('Il nome del responsabile è obbligatorio');\n        return;\n      }\n      \n      if (!formDataResponsabile.email && !formDataResponsabile.telefono) {\n        setError('Almeno uno tra email e telefono deve essere specificato');\n        return;\n      }\n\n      if (dialogModeResponsabile === 'create') {\n        await responsabiliService.createResponsabile(cantiereId, formDataResponsabile);\n      } else if (dialogModeResponsabile === 'edit') {\n        await responsabiliService.updateResponsabile(selectedResponsabile.id_responsabile, formDataResponsabile);\n      }\n\n      handleCloseResponsabileDialog();\n      await loadResponsabili();\n    } catch (err) {\n      console.error('Errore nel salvataggio:', err);\n      setError(err.detail || 'Errore nel salvataggio del responsabile');\n    }\n  };\n\n  const handleDeleteResponsabile = async (idResponsabile) => {\n    if (!window.confirm('Sei sicuro di voler eliminare questo responsabile?')) {\n      return;\n    }\n\n    try {\n      await responsabiliService.deleteResponsabile(idResponsabile);\n      await loadResponsabili();\n    } catch (err) {\n      console.error('Errore nell\\'eliminazione:', err);\n      setError('Errore nell\\'eliminazione del responsabile');\n    }\n  };\n\n  // Gestione comande\n  const handleOpenComandaDialog = (mode, comanda = null) => {\n    setDialogModeComanda(mode);\n    setSelectedComanda(comanda);\n\n    if (mode === 'edit' && comanda) {\n      setFormDataComanda({\n        tipo_comanda: comanda.tipo_comanda,\n        descrizione: comanda.descrizione || '',\n        responsabile: comanda.responsabile || '',\n        data_scadenza: comanda.data_scadenza || '',\n        priorita: comanda.priorita || 'NORMALE',\n        note_capo_cantiere: comanda.note_capo_cantiere || ''\n      });\n    }\n\n    setOpenComandaDialog(true);\n  };\n\n  const handleCloseComandaDialog = () => {\n    setOpenComandaDialog(false);\n    setSelectedComanda(null);\n    setFormDataComanda({\n      tipo_comanda: 'POSA',\n      descrizione: '',\n      responsabile: '',\n      data_scadenza: '',\n      priorita: 'NORMALE',\n      note_capo_cantiere: ''\n    });\n  };\n\n  const handleSubmitComanda = async () => {\n    try {\n      if (dialogModeComanda === 'edit') {\n        await comandeService.updateComanda(selectedComanda.codice_comanda, formDataComanda);\n        handleCloseComandaDialog();\n        await loadResponsabili(); // Ricarica per aggiornare le comande\n        await loadStatistiche();\n      }\n    } catch (err) {\n      console.error('Errore nel salvataggio:', err);\n      setError('Errore nel salvataggio della comanda');\n    }\n  };\n\n  const handleDeleteComanda = async (codiceComanda) => {\n    if (!window.confirm('Sei sicuro di voler eliminare questa comanda?')) {\n      return;\n    }\n\n    try {\n      await comandeService.deleteComanda(codiceComanda);\n      await loadResponsabili(); // Ricarica per aggiornare le comande\n      await loadStatistiche();\n    } catch (err) {\n      console.error('Errore nell\\'eliminazione:', err);\n      setError('Errore nell\\'eliminazione della comanda');\n    }\n  };\n\n  const getTipoComandaLabel = (tipo) => {\n    const labels = {\n      'POSA': 'Posa',\n      'COLLEGAMENTO_PARTENZA': 'Coll. Partenza',\n      'COLLEGAMENTO_ARRIVO': 'Coll. Arrivo',\n      'CERTIFICAZIONE': 'Certificazione',\n      'TESTING': 'Testing/Certificazione'\n    };\n    return labels[tipo] || tipo;\n  };\n\n  const getStatoColor = (stato) => {\n    const colors = {\n      'CREATA': 'default',\n      'ASSEGNATA': 'primary',\n      'IN_CORSO': 'warning',\n      'COMPLETATA': 'success',\n      'ANNULLATA': 'error'\n    };\n    return colors[stato] || 'default';\n  };\n\n  const getPrioritaColor = (priorita) => {\n    const colors = {\n      'BASSA': 'default',\n      'NORMALE': 'primary',\n      'ALTA': 'warning',\n      'URGENTE': 'error'\n    };\n    return colors[priorita] || 'default';\n  };\n\n  if (loading) {\n    return (\n      <Box display=\"flex\" justifyContent=\"center\" alignItems=\"center\" minHeight=\"400px\">\n        <CircularProgress />\n      </Box>\n    );\n  }\n\n  return (\n    <Box sx={{ p: 3 }}>\n      {/* Header */}\n      <Box mb={3}>\n        <Typography variant=\"h4\" sx={{ fontWeight: 600, color: 'primary.main' }}>\n          {cantiereName}\n        </Typography>\n      </Box>\n\n      {error && (\n        <Alert severity=\"error\" sx={{ mb: 2 }}>\n          {error}\n        </Alert>\n      )}\n\n      {/* Statistiche in stile Visualizza Cavi */}\n      {statistiche && (\n        <Paper sx={{ p: 2, mb: 3, bgcolor: 'grey.50' }}>\n          <Stack direction=\"row\" spacing={4} alignItems=\"center\" justifyContent=\"space-between\" flexWrap=\"wrap\">\n            {/* Responsabili */}\n            <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n              <PersonIcon color=\"primary\" fontSize=\"small\" />\n              <Box>\n                <Typography variant=\"h6\" fontWeight=\"bold\" sx={{ lineHeight: 1 }}>\n                  {statistiche.responsabili_attivi || 0}\n                </Typography>\n                <Typography variant=\"caption\" color=\"text.secondary\">\n                  Responsabili\n                </Typography>\n              </Box>\n            </Stack>\n\n            {/* Totale Comande */}\n            <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n              <AssignIcon color=\"info\" fontSize=\"small\" />\n              <Box>\n                <Typography variant=\"h6\" fontWeight=\"bold\" sx={{ lineHeight: 1 }}>\n                  {statistiche.totale_comande || 0}\n                </Typography>\n                <Typography variant=\"caption\" color=\"text.secondary\">\n                  Totale\n                </Typography>\n              </Box>\n            </Stack>\n\n            {/* In Corso */}\n            <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n              <CheckCircleIcon color=\"warning\" fontSize=\"small\" />\n              <Box>\n                <Typography variant=\"h6\" fontWeight=\"bold\" sx={{ lineHeight: 1 }}>\n                  {statistiche.comande_in_corso || 0}\n                </Typography>\n                <Typography variant=\"caption\" color=\"text.secondary\">\n                  In Corso\n                </Typography>\n              </Box>\n            </Stack>\n\n            {/* Completate */}\n            <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n              <VerifiedIcon color=\"success\" fontSize=\"small\" />\n              <Box>\n                <Typography variant=\"h6\" fontWeight=\"bold\" sx={{ lineHeight: 1 }}>\n                  {statistiche.comande_completate || 0}\n                </Typography>\n                <Typography variant=\"caption\" color=\"text.secondary\">\n                  Completate\n                </Typography>\n              </Box>\n            </Stack>\n\n            {/* Percentuale completamento */}\n            <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n              <Box sx={{\n                width: 32,\n                height: 32,\n                borderRadius: '50%',\n                bgcolor: (statistiche.comande_completate / (statistiche.totale_comande || 1)) >= 0.8 ? 'success.main' :\n                         (statistiche.comande_completate / (statistiche.totale_comande || 1)) >= 0.5 ? 'warning.main' : 'error.main',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center'\n              }}>\n                <Typography variant=\"caption\" fontWeight=\"bold\" color=\"white\">\n                  {statistiche.totale_comande > 0 ? Math.round((statistiche.comande_completate / statistiche.totale_comande) * 100) : 0}%\n                </Typography>\n              </Box>\n              <Box>\n                <Typography variant=\"body2\" fontWeight=\"medium\" sx={{ lineHeight: 1 }}>\n                  Completamento\n                </Typography>\n                <Typography variant=\"caption\" color=\"text.secondary\">\n                  {statistiche.comande_create || 0} create\n                </Typography>\n              </Box>\n            </Stack>\n          </Stack>\n        </Paper>\n      )}\n\n      {/* Sezione Responsabili - Elemento Principale */}\n      <Box>\n        <Box>\n          {/* Toolbar Responsabili */}\n          <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\" mb={3}>\n            <Typography variant=\"h5\" sx={{ fontWeight: 500, color: 'text.primary' }}>\n              Responsabili del Cantiere\n            </Typography>\n            <Button\n              variant=\"contained\"\n              startIcon={<AddIcon />}\n              onClick={() => handleOpenResponsabileDialog('create')}\n              sx={{\n                textTransform: 'none',\n                fontWeight: 500,\n                px: 3,\n                py: 1,\n                backgroundColor: '#f8f9fa',\n                color: '#212529',\n                border: '1px solid #dee2e6',\n                boxShadow: 'none',\n                '&:hover': {\n                  backgroundColor: '#e9ecef',\n                  boxShadow: 'none'\n                }\n              }}\n            >\n              Inserisci Responsabile\n            </Button>\n          </Box>\n\n          {loadingResponsabili ? (\n            <Box display=\"flex\" justifyContent=\"center\" py={4}>\n              <CircularProgress />\n            </Box>\n          ) : (\n            <Box>\n              {responsabili.length === 0 ? (\n                <Paper\n                  elevation={0}\n                  sx={{\n                    p: 6,\n                    textAlign: 'center',\n                    backgroundColor: 'grey.50',\n                    border: '1px dashed',\n                    borderColor: 'grey.300'\n                  }}\n                >\n                  <PersonIcon sx={{ fontSize: 48, color: 'grey.400', mb: 2 }} />\n                  <Typography variant=\"h6\" color=\"text.secondary\" gutterBottom>\n                    Nessun responsabile configurato\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 3 }}>\n                    Aggiungi il primo responsabile per iniziare a gestire le comande\n                  </Typography>\n                  <Button\n                    variant=\"contained\"\n                    startIcon={<AddIcon />}\n                    onClick={() => handleOpenResponsabileDialog('create')}\n                    sx={{\n                      textTransform: 'none',\n                      backgroundColor: '#f8f9fa',\n                      color: '#212529',\n                      border: '1px solid #dee2e6',\n                      boxShadow: 'none',\n                      '&:hover': {\n                        backgroundColor: '#e9ecef',\n                        boxShadow: 'none'\n                      }\n                    }}\n                  >\n                    Inserisci Primo Responsabile\n                  </Button>\n                </Paper>\n              ) : (\n                responsabili.map((responsabile) => (\n                  <Accordion\n                    key={responsabile.id_responsabile}\n                    sx={{\n                      mb: 2,\n                      '&:before': { display: 'none' },\n                      boxShadow: '0 1px 3px rgba(0,0,0,0.1)',\n                      border: '1px solid',\n                      borderColor: 'grey.200'\n                    }}\n                  >\n                    <AccordionSummary\n                      expandIcon={<ExpandMoreIcon />}\n                      sx={{\n                        '&:hover': {\n                          backgroundColor: 'grey.50'\n                        }\n                      }}\n                    >\n                      <Box display=\"flex\" alignItems=\"center\" justifyContent=\"space-between\" width=\"100%\">\n                        <Box display=\"flex\" alignItems=\"center\" gap={2}>\n                          <PersonIcon color=\"primary\" sx={{ fontSize: 28 }} />\n                          <Box>\n                            <Typography variant=\"h6\" sx={{ fontWeight: 500 }}>\n                              {responsabile.nome_responsabile}\n                            </Typography>\n                            <Box display=\"flex\" gap={3} mt={0.5}>\n                              {responsabile.email && (\n                                <Box display=\"flex\" alignItems=\"center\" gap={0.5}>\n                                  <EmailIcon fontSize=\"small\" color=\"action\" />\n                                  <Typography variant=\"body2\" color=\"text.secondary\">\n                                    {responsabile.email}\n                                  </Typography>\n                                </Box>\n                              )}\n                              {responsabile.telefono && (\n                                <Box display=\"flex\" alignItems=\"center\" gap={0.5}>\n                                  <PhoneIcon fontSize=\"small\" color=\"action\" />\n                                  <Typography variant=\"body2\" color=\"text.secondary\">\n                                    {responsabile.telefono}\n                                  </Typography>\n                                </Box>\n                              )}\n                            </Box>\n                          </Box>\n                        </Box>\n\n                        <Box display=\"flex\" alignItems=\"center\" gap={1} onClick={(e) => e.stopPropagation()}>\n                          <Chip\n                            icon={<AssignIcon />}\n                            label={`${Array.isArray(comandePerResponsabile[responsabile.id_responsabile]) ? comandePerResponsabile[responsabile.id_responsabile].length : 0} comande`}\n                            size=\"small\"\n                            color=\"primary\"\n                            variant=\"outlined\"\n                            clickable\n                            onClick={() => {\n                              // Pre-seleziona il responsabile nel dialog di creazione comanda\n                              setOpenCreaConCavi(true);\n                            }}\n                            sx={{\n                              fontWeight: 500,\n                              backgroundColor: 'rgba(33, 150, 243, 0.1)',\n                              color: '#1976d2',\n                              border: '1px solid rgba(33, 150, 243, 0.3)',\n                              '&:hover': {\n                                backgroundColor: 'rgba(33, 150, 243, 0.2) !important',\n                                color: '#1565c0 !important'\n                              }\n                            }}\n                          />\n                          <Tooltip title=\"Modifica responsabile\">\n                            <IconButton\n                              size=\"small\"\n                              onClick={() => handleOpenResponsabileDialog('edit', responsabile)}\n                              sx={{\n                                '&:hover': {\n                                  backgroundColor: 'primary.light',\n                                  color: 'white'\n                                }\n                              }}\n                            >\n                              <EditIcon fontSize=\"small\" />\n                            </IconButton>\n                          </Tooltip>\n                          <Tooltip title=\"Elimina responsabile\">\n                            <IconButton\n                              size=\"small\"\n                              onClick={() => handleDeleteResponsabile(responsabile.id_responsabile)}\n                              sx={{\n                                '&:hover': {\n                                  backgroundColor: 'error.light',\n                                  color: 'white'\n                                }\n                              }}\n                            >\n                              <DeleteIcon fontSize=\"small\" />\n                            </IconButton>\n                          </Tooltip>\n                        </Box>\n                      </Box>\n                    </AccordionSummary>\n\n                    <AccordionDetails sx={{ pt: 2 }}>\n                      <Typography variant=\"subtitle2\" gutterBottom sx={{ fontWeight: 500, mb: 2 }}>\n                        Comande Assegnate\n                      </Typography>\n\n                      {(!Array.isArray(comandePerResponsabile[responsabile.id_responsabile]) || comandePerResponsabile[responsabile.id_responsabile].length === 0) ? (\n                        <Box\n                          sx={{\n                            p: 3,\n                            textAlign: 'center',\n                            backgroundColor: 'grey.50',\n                            borderRadius: 1,\n                            border: '1px dashed',\n                            borderColor: 'grey.300'\n                          }}\n                        >\n                          <Typography variant=\"body2\" color=\"text.secondary\">\n                            Nessuna comanda assegnata a questo responsabile\n                          </Typography>\n                        </Box>\n                      ) : (\n                        <List dense>\n                          {Array.isArray(comandePerResponsabile[responsabile.id_responsabile]) && comandePerResponsabile[responsabile.id_responsabile].map((comanda) => (\n                            <ListItem\n                              key={comanda.codice_comanda}\n                              divider\n                              sx={{\n                                '&:hover': {\n                                  backgroundColor: 'rgba(33, 150, 243, 0.1)'\n                                }\n                              }}\n                            >\n                              <ListItemText\n                                primary={\n                                  <Box display=\"flex\" alignItems=\"center\" gap={1}>\n                                    <Typography variant=\"body2\" fontWeight=\"bold\">\n                                      {comanda.codice_comanda}\n                                    </Typography>\n                                    <Chip\n                                      label={getTipoComandaLabel(comanda.tipo_comanda)}\n                                      size=\"small\"\n                                      variant=\"outlined\"\n                                    />\n                                    <Chip\n                                      label={comanda.stato || 'CREATA'}\n                                      size=\"small\"\n                                      color={getStatoColor(comanda.stato)}\n                                    />\n                                    {comanda.priorita && comanda.priorita !== 'NORMALE' && (\n                                      <Chip\n                                        label={comanda.priorita}\n                                        size=\"small\"\n                                        color={getPrioritaColor(comanda.priorita)}\n                                        variant=\"filled\"\n                                      />\n                                    )}\n                                  </Box>\n                                }\n                                secondary={\n                                  <Box>\n                                    <Typography variant=\"body2\" color=\"textSecondary\">\n                                      {comanda.descrizione || 'Nessuna descrizione'}\n                                      {comanda.data_creazione && ` • Creata: ${new Date(comanda.data_creazione).toLocaleDateString()}`}\n                                    </Typography>\n                                    {comanda.numero_cavi_assegnati > 0 && (\n                                      <Typography variant=\"caption\" color=\"primary\">\n                                        {comanda.numero_cavi_assegnati} cavi assegnati\n                                        {comanda.percentuale_completamento && ` • ${comanda.percentuale_completamento.toFixed(1)}% completato`}\n                                      </Typography>\n                                    )}\n                                  </Box>\n                                }\n                              />\n                              <Box display=\"flex\" gap={0.5} ml={1}>\n                                <Tooltip title=\"Visualizza\">\n                                  <IconButton\n                                    size=\"small\"\n                                    onClick={() => handleOpenComandaDialog('view', comanda)}\n                                    sx={{\n                                      '&:hover': {\n                                        backgroundColor: '#2196f3',\n                                        color: 'white'\n                                      }\n                                    }}\n                                  >\n                                    <ViewIcon fontSize=\"small\" />\n                                  </IconButton>\n                                </Tooltip>\n                                <Tooltip title=\"Modifica\">\n                                  <IconButton\n                                    size=\"small\"\n                                    onClick={() => handleOpenComandaDialog('edit', comanda)}\n                                    sx={{\n                                      '&:hover': {\n                                        backgroundColor: '#1976d2',\n                                        color: 'white'\n                                      }\n                                    }}\n                                  >\n                                    <EditIcon fontSize=\"small\" />\n                                  </IconButton>\n                                </Tooltip>\n                                <Tooltip title=\"Elimina\">\n                                  <IconButton\n                                    size=\"small\"\n                                    onClick={() => handleDeleteComanda(comanda.codice_comanda)}\n                                    sx={{\n                                      '&:hover': {\n                                        backgroundColor: 'error.main',\n                                        color: 'white'\n                                      }\n                                    }}\n                                  >\n                                    <DeleteIcon fontSize=\"small\" />\n                                  </IconButton>\n                                </Tooltip>\n                              </Box>\n                            </ListItem>\n                          ))}\n                        </List>\n                      )}\n                    </AccordionDetails>\n                  </Accordion>\n                ))\n              )}\n            </Box>\n          )}\n        </Box>\n      </Box>\n\n      {/* Dialog per creazione/modifica responsabile */}\n      <Dialog\n        open={openResponsabileDialog}\n        onClose={handleCloseResponsabileDialog}\n        maxWidth=\"sm\"\n        fullWidth\n        PaperProps={{\n          sx: { borderRadius: 2 }\n        }}\n      >\n        <DialogTitle sx={{ pb: 1 }}>\n          <Typography variant=\"h6\" sx={{ fontWeight: 600 }}>\n            {dialogModeResponsabile === 'create' ? 'Inserisci Responsabile' : 'Modifica Responsabile'}\n          </Typography>\n        </DialogTitle>\n        <DialogContent>\n          <Box sx={{ pt: 2 }}>\n            <TextField\n              fullWidth\n              label=\"Nome Responsabile\"\n              value={formDataResponsabile.nome_responsabile}\n              onChange={(e) => setFormDataResponsabile({ ...formDataResponsabile, nome_responsabile: e.target.value })}\n              margin=\"normal\"\n              required\n              variant=\"outlined\"\n              sx={{ mb: 2 }}\n            />\n\n            <TextField\n              fullWidth\n              label=\"Email\"\n              type=\"email\"\n              value={formDataResponsabile.email}\n              onChange={(e) => setFormDataResponsabile({ ...formDataResponsabile, email: e.target.value })}\n              margin=\"normal\"\n              variant=\"outlined\"\n              helperText=\"Email per notifiche (opzionale se inserisci telefono)\"\n              sx={{ mb: 2 }}\n            />\n\n            <TextField\n              fullWidth\n              label=\"Telefono\"\n              value={formDataResponsabile.telefono}\n              onChange={(e) => setFormDataResponsabile({ ...formDataResponsabile, telefono: e.target.value })}\n              margin=\"normal\"\n              variant=\"outlined\"\n              helperText=\"Numero per SMS (opzionale se inserisci email)\"\n            />\n          </Box>\n        </DialogContent>\n        <DialogActions sx={{ p: 3, pt: 2 }}>\n          <Button\n            onClick={handleCloseResponsabileDialog}\n            sx={{ textTransform: 'none' }}\n          >\n            Annulla\n          </Button>\n          <Button\n            onClick={handleSubmitResponsabile}\n            variant=\"contained\"\n            sx={{\n              textTransform: 'none',\n              fontWeight: 500,\n              px: 3\n            }}\n          >\n            {dialogModeResponsabile === 'create' ? 'Crea' : 'Salva'}\n          </Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* Dialog per visualizzazione/modifica comanda */}\n      <Dialog\n        open={openComandaDialog}\n        onClose={handleCloseComandaDialog}\n        maxWidth=\"md\"\n        fullWidth\n        PaperProps={{\n          sx: { borderRadius: 2 }\n        }}\n      >\n        <DialogTitle sx={{ pb: 1 }}>\n          <Typography variant=\"h6\" sx={{ fontWeight: 600 }}>\n            {dialogModeComanda === 'view' ? 'Dettagli Comanda' : 'Modifica Comanda'}\n          </Typography>\n        </DialogTitle>\n        <DialogContent>\n          <Box sx={{ pt: 2 }}>\n            {dialogModeComanda === 'view' && selectedComanda ? (\n              <List>\n                <ListItem>\n                  <ListItemText\n                    primary=\"Codice Comanda\"\n                    secondary={selectedComanda.codice_comanda}\n                  />\n                </ListItem>\n                <Divider />\n                <ListItem>\n                  <ListItemText\n                    primary=\"Tipo\"\n                    secondary={getTipoComandaLabel(selectedComanda.tipo_comanda)}\n                  />\n                </ListItem>\n                <Divider />\n                <ListItem>\n                  <ListItemText\n                    primary=\"Stato\"\n                    secondary={\n                      <Chip\n                        label={selectedComanda.stato}\n                        color={getStatoColor(selectedComanda.stato)}\n                        size=\"small\"\n                      />\n                    }\n                  />\n                </ListItem>\n                <Divider />\n                <ListItem>\n                  <ListItemText\n                    primary=\"Descrizione\"\n                    secondary={selectedComanda.descrizione || 'Nessuna descrizione'}\n                  />\n                </ListItem>\n                <Divider />\n                <ListItem>\n                  <ListItemText\n                    primary=\"Priorità\"\n                    secondary={\n                      <Chip\n                        label={selectedComanda.priorita || 'NORMALE'}\n                        color={getPrioritaColor(selectedComanda.priorita || 'NORMALE')}\n                        size=\"small\"\n                      />\n                    }\n                  />\n                </ListItem>\n                <Divider />\n                <ListItem>\n                  <ListItemText\n                    primary=\"Responsabile\"\n                    secondary={selectedComanda.responsabile || 'Non assegnato'}\n                  />\n                </ListItem>\n                {selectedComanda.note_capo_cantiere && (\n                  <>\n                    <Divider />\n                    <ListItem>\n                      <ListItemText\n                        primary=\"Note Capo Cantiere\"\n                        secondary={selectedComanda.note_capo_cantiere}\n                      />\n                    </ListItem>\n                  </>\n                )}\n                <Divider />\n                <ListItem>\n                  <ListItemText\n                    primary=\"Data Creazione\"\n                    secondary={new Date(selectedComanda.data_creazione).toLocaleDateString('it-IT')}\n                  />\n                </ListItem>\n                {selectedComanda.data_scadenza && (\n                  <>\n                    <Divider />\n                    <ListItem>\n                      <ListItemText\n                        primary=\"Data Scadenza\"\n                        secondary={new Date(selectedComanda.data_scadenza).toLocaleDateString('it-IT')}\n                      />\n                    </ListItem>\n                  </>\n                )}\n                <Divider />\n                <ListItem>\n                  <ListItemText\n                    primary=\"Cavi Assegnati\"\n                    secondary={selectedComanda.numero_cavi_assegnati || 0}\n                  />\n                </ListItem>\n                <Divider />\n                <ListItem>\n                  <ListItemText\n                    primary=\"Completamento\"\n                    secondary={`${(selectedComanda.percentuale_completamento || 0).toFixed(1)}%`}\n                  />\n                </ListItem>\n              </List>\n            ) : (\n              <>\n                <TextField\n                  fullWidth\n                  select\n                  label=\"Tipo Comanda\"\n                  value={formDataComanda.tipo_comanda}\n                  onChange={(e) => setFormDataComanda({ ...formDataComanda, tipo_comanda: e.target.value })}\n                  margin=\"normal\"\n                  sx={{ mb: 2 }}\n                >\n                  <MenuItem value=\"POSA\">Posa</MenuItem>\n                  <MenuItem value=\"COLLEGAMENTO_PARTENZA\">Collegamento Partenza</MenuItem>\n                  <MenuItem value=\"COLLEGAMENTO_ARRIVO\">Collegamento Arrivo</MenuItem>\n                  <MenuItem value=\"CERTIFICAZIONE\">Certificazione</MenuItem>\n                  <MenuItem value=\"TESTING\">Testing</MenuItem>\n                </TextField>\n\n                <TextField\n                  fullWidth\n                  select\n                  label=\"Priorità\"\n                  value={formDataComanda.priorita}\n                  onChange={(e) => setFormDataComanda({ ...formDataComanda, priorita: e.target.value })}\n                  margin=\"normal\"\n                  sx={{ mb: 2 }}\n                >\n                  <MenuItem value=\"BASSA\">Bassa</MenuItem>\n                  <MenuItem value=\"NORMALE\">Normale</MenuItem>\n                  <MenuItem value=\"ALTA\">Alta</MenuItem>\n                  <MenuItem value=\"URGENTE\">Urgente</MenuItem>\n                </TextField>\n\n                <TextField\n                  fullWidth\n                  label=\"Descrizione\"\n                  value={formDataComanda.descrizione}\n                  onChange={(e) => setFormDataComanda({ ...formDataComanda, descrizione: e.target.value })}\n                  margin=\"normal\"\n                  multiline\n                  rows={3}\n                  sx={{ mb: 2 }}\n                />\n\n                <TextField\n                  fullWidth\n                  label=\"Responsabile\"\n                  value={formDataComanda.responsabile}\n                  onChange={(e) => setFormDataComanda({ ...formDataComanda, responsabile: e.target.value })}\n                  margin=\"normal\"\n                  required\n                  helperText=\"Chi eseguirà il lavoro (obbligatorio)\"\n                  sx={{ mb: 2 }}\n                />\n\n                <TextField\n                  fullWidth\n                  label=\"Note Capo Cantiere\"\n                  value={formDataComanda.note_capo_cantiere}\n                  onChange={(e) => setFormDataComanda({ ...formDataComanda, note_capo_cantiere: e.target.value })}\n                  margin=\"normal\"\n                  multiline\n                  rows={2}\n                  helperText=\"Istruzioni specifiche per il responsabile\"\n                  sx={{ mb: 2 }}\n                />\n\n                <TextField\n                  fullWidth\n                  label=\"Data Scadenza\"\n                  type=\"date\"\n                  value={formDataComanda.data_scadenza}\n                  onChange={(e) => setFormDataComanda({ ...formDataComanda, data_scadenza: e.target.value })}\n                  margin=\"normal\"\n                  InputLabelProps={{\n                    shrink: true,\n                  }}\n                />\n              </>\n            )}\n          </Box>\n        </DialogContent>\n        <DialogActions sx={{ p: 3, pt: 2 }}>\n          <Button\n            onClick={handleCloseComandaDialog}\n            sx={{ textTransform: 'none' }}\n          >\n            {dialogModeComanda === 'view' ? 'Chiudi' : 'Annulla'}\n          </Button>\n          {dialogModeComanda === 'edit' && (\n            <Button\n              onClick={handleSubmitComanda}\n              variant=\"contained\"\n              sx={{\n                textTransform: 'none',\n                fontWeight: 500,\n                px: 3\n              }}\n            >\n              Salva Modifiche\n            </Button>\n          )}\n        </DialogActions>\n      </Dialog>\n\n      {/* Dialog CreaComandaConCavi */}\n      <CreaComandaConCavi\n        cantiereId={cantiereId}\n        open={openCreaConCavi}\n        onClose={() => setOpenCreaConCavi(false)}\n        onSuccess={() => {\n          loadComande();\n          loadStatistiche();\n          loadResponsabili();\n          setOpenCreaConCavi(false);\n        }}\n      />\n    </Box>\n  );\n};\n\nexport default ComandeListRivoluzionato;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,IAAI,EACJC,UAAU,EACVC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,SAAS,EACTC,KAAK,EACLC,gBAAgB,EAChBC,OAAO,EACPC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,SAAS,EACTC,gBAAgB,EAChBC,gBAAgB,EAChBC,KAAK,EACLC,QAAQ,EACRC,OAAO,QACF,eAAe;AACtB,SACEC,GAAG,IAAIC,OAAO,EACdC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,UAAU,IAAIC,UAAU,EACxBC,MAAM,IAAIC,UAAU,EACpBC,KAAK,IAAIC,SAAS,EAClBC,KAAK,IAAIC,SAAS,EAClBC,UAAU,IAAIC,cAAc,EAC5BC,WAAW,IAAIC,eAAe,EAC9BC,QAAQ,IAAIC,YAAY,EACxBC,UAAU,IAAIC,QAAQ,QACjB,qBAAqB;AAC5B,OAAOC,cAAc,MAAM,+BAA+B;AAC1D,OAAOC,mBAAmB,MAAM,oCAAoC;AACpE,OAAOC,kBAAkB,MAAM,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEtD,MAAMC,wBAAwB,GAAGA,CAAC;EAAEC,UAAU;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EACjE;EACA,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG3D,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC4D,KAAK,EAAEC,QAAQ,CAAC,GAAG7D,QAAQ,CAAC,IAAI,CAAC;;EAExC;EACA,MAAM,CAAC8D,WAAW,EAAEC,cAAc,CAAC,GAAG/D,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACgE,eAAe,EAAEC,kBAAkB,CAAC,GAAGjE,QAAQ,CAAC,KAAK,CAAC;;EAE7D;EACA,MAAM,CAACkE,YAAY,EAAEC,eAAe,CAAC,GAAGnE,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACoE,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGrE,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACsE,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGvE,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxE,MAAM,CAACwE,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGzE,QAAQ,CAAC,KAAK,CAAC;EAC3E,MAAM,CAAC0E,sBAAsB,EAAEC,yBAAyB,CAAC,GAAG3E,QAAQ,CAAC,QAAQ,CAAC;EAC9E,MAAM,CAAC4E,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG7E,QAAQ,CAAC,IAAI,CAAC;EACtE,MAAM,CAAC8E,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG/E,QAAQ,CAAC;IAC/DgF,iBAAiB,EAAE,EAAE;IACrBC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE;EACZ,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGpF,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACqF,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGtF,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;EACpE,MAAM,CAACuF,eAAe,EAAEC,kBAAkB,CAAC,GAAGxF,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACyF,eAAe,EAAEC,kBAAkB,CAAC,GAAG1F,QAAQ,CAAC;IACrD2F,YAAY,EAAE,MAAM;IACpBC,WAAW,EAAE,EAAE;IACfC,YAAY,EAAE,EAAE;IAChBC,aAAa,EAAE,EAAE;IACjBC,QAAQ,EAAE,SAAS;IACnBC,kBAAkB,EAAE;EACtB,CAAC,CAAC;EAEF,MAAMC,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACFtC,UAAU,CAAC,IAAI,CAAC;MAChB;MACA;MACAE,QAAQ,CAAC,IAAI,CAAC;IAChB,CAAC,CAAC,OAAOqC,GAAG,EAAE;MACZC,OAAO,CAACvC,KAAK,CAAC,uCAAuC,EAAEsC,GAAG,CAAC;MAC3DrC,QAAQ,CAAC,sCAAsC,CAAC;IAClD,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMyC,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF,MAAMC,KAAK,GAAG,MAAMtD,cAAc,CAACuD,qBAAqB,CAAC/C,UAAU,CAAC;MACpEQ,cAAc,CAACsC,KAAK,CAAC;IACvB,CAAC,CAAC,OAAOH,GAAG,EAAE;MACZC,OAAO,CAACvC,KAAK,CAAC,2CAA2C,EAAEsC,GAAG,CAAC;IACjE;EACF,CAAC;EAED,MAAMK,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACFlC,sBAAsB,CAAC,IAAI,CAAC;MAC5BR,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAM2C,IAAI,GAAG,MAAMxD,mBAAmB,CAACyD,uBAAuB,CAAClD,UAAU,CAAC;MAC1EY,eAAe,CAACqC,IAAI,IAAI,EAAE,CAAC;MAC3B,MAAME,0BAA0B,CAACF,IAAI,IAAI,EAAE,CAAC;IAC9C,CAAC,CAAC,OAAON,GAAG,EAAE;MAAA,IAAAS,aAAA,EAAAC,kBAAA;MACZT,OAAO,CAACvC,KAAK,CAAC,0CAA0C,EAAEsC,GAAG,CAAC;MAC9D,MAAMW,YAAY,GAAG,EAAAF,aAAA,GAAAT,GAAG,CAACY,QAAQ,cAAAH,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAAcH,IAAI,cAAAI,kBAAA,uBAAlBA,kBAAA,CAAoBG,MAAM,KAAIb,GAAG,CAACc,OAAO,IAAI,yCAAyC;MAC3GnD,QAAQ,CAAC,4CAA4CgD,YAAY,EAAE,CAAC;IACtE,CAAC,SAAS;MACRxC,sBAAsB,CAAC,KAAK,CAAC;IAC/B;EACF,CAAC;;EAED;EACApE,SAAS,CAAC,MAAM;IACd,IAAIsD,UAAU,EAAE;MACdgD,gBAAgB,CAAC,CAAC;MAClBN,WAAW,CAAC,CAAC;MACbG,eAAe,CAAC,CAAC;IACnB;EACF,CAAC,EAAE,CAAC7C,UAAU,CAAC,CAAC;EAEhB,MAAMmD,0BAA0B,GAAG,MAAOO,gBAAgB,IAAK;IAC7D,IAAI;MACF,MAAMC,UAAU,GAAG,CAAC,CAAC;MACrB,KAAK,MAAMrB,YAAY,IAAIoB,gBAAgB,EAAE;QAC3C,IAAI;UACF,MAAMH,QAAQ,GAAG,MAAM/D,cAAc,CAACoE,wBAAwB,CAAC5D,UAAU,EAAEsC,YAAY,CAACb,iBAAiB,CAAC;UAC1G;UACA,IAAIoC,OAAO,GAAG,EAAE;UAChB,IAAIN,QAAQ,IAAIO,KAAK,CAACC,OAAO,CAACR,QAAQ,CAAC,EAAE;YACvCM,OAAO,GAAGN,QAAQ;UACpB,CAAC,MAAM,IAAIA,QAAQ,IAAIA,QAAQ,CAACM,OAAO,IAAIC,KAAK,CAACC,OAAO,CAACR,QAAQ,CAACM,OAAO,CAAC,EAAE;YAC1EA,OAAO,GAAGN,QAAQ,CAACM,OAAO;UAC5B,CAAC,MAAM,IAAIN,QAAQ,IAAIA,QAAQ,CAACN,IAAI,IAAIa,KAAK,CAACC,OAAO,CAACR,QAAQ,CAACN,IAAI,CAAC,EAAE;YACpEY,OAAO,GAAGN,QAAQ,CAACN,IAAI;UACzB;UACAU,UAAU,CAACrB,YAAY,CAAC0B,eAAe,CAAC,GAAGH,OAAO;QACpD,CAAC,CAAC,OAAOlB,GAAG,EAAE;UACZC,OAAO,CAACvC,KAAK,CAAC,sCAAsCiC,YAAY,CAACb,iBAAiB,GAAG,EAAEkB,GAAG,CAAC;UAC3FgB,UAAU,CAACrB,YAAY,CAAC0B,eAAe,CAAC,GAAG,EAAE;QAC/C;MACF;MACAhD,yBAAyB,CAAC2C,UAAU,CAAC;IACvC,CAAC,CAAC,OAAOhB,GAAG,EAAE;MACZC,OAAO,CAACvC,KAAK,CAAC,uCAAuC,EAAEsC,GAAG,CAAC;IAC7D;EACF,CAAC;;EAED;EACA,MAAMsB,4BAA4B,GAAGA,CAACC,IAAI,EAAE5B,YAAY,GAAG,IAAI,KAAK;IAClElB,yBAAyB,CAAC8C,IAAI,CAAC;IAC/B5C,uBAAuB,CAACgB,YAAY,CAAC;IAErC,IAAI4B,IAAI,KAAK,MAAM,IAAI5B,YAAY,EAAE;MACnCd,uBAAuB,CAAC;QACtBC,iBAAiB,EAAEa,YAAY,CAACb,iBAAiB,IAAI,EAAE;QACvDC,KAAK,EAAEY,YAAY,CAACZ,KAAK,IAAI,EAAE;QAC/BC,QAAQ,EAAEW,YAAY,CAACX,QAAQ,IAAI;MACrC,CAAC,CAAC;IACJ,CAAC,MAAM;MACLH,uBAAuB,CAAC;QACtBC,iBAAiB,EAAE,EAAE;QACrBC,KAAK,EAAE,EAAE;QACTC,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ;IAEAT,yBAAyB,CAAC,IAAI,CAAC;EACjC,CAAC;EAED,MAAMiD,6BAA6B,GAAGA,CAAA,KAAM;IAC1CjD,yBAAyB,CAAC,KAAK,CAAC;IAChCI,uBAAuB,CAAC,IAAI,CAAC;IAC7BhB,QAAQ,CAAC,IAAI,CAAC;EAChB,CAAC;EAED,MAAM8D,wBAAwB,GAAG,MAAAA,CAAA,KAAY;IAC3C,IAAI;MACF9D,QAAQ,CAAC,IAAI,CAAC;MAEd,IAAI,CAACiB,oBAAoB,CAACE,iBAAiB,CAAC4C,IAAI,CAAC,CAAC,EAAE;QAClD/D,QAAQ,CAAC,yCAAyC,CAAC;QACnD;MACF;MAEA,IAAI,CAACiB,oBAAoB,CAACG,KAAK,IAAI,CAACH,oBAAoB,CAACI,QAAQ,EAAE;QACjErB,QAAQ,CAAC,yDAAyD,CAAC;QACnE;MACF;MAEA,IAAIa,sBAAsB,KAAK,QAAQ,EAAE;QACvC,MAAM1B,mBAAmB,CAAC6E,kBAAkB,CAACtE,UAAU,EAAEuB,oBAAoB,CAAC;MAChF,CAAC,MAAM,IAAIJ,sBAAsB,KAAK,MAAM,EAAE;QAC5C,MAAM1B,mBAAmB,CAAC8E,kBAAkB,CAAClD,oBAAoB,CAAC2C,eAAe,EAAEzC,oBAAoB,CAAC;MAC1G;MAEA4C,6BAA6B,CAAC,CAAC;MAC/B,MAAMnB,gBAAgB,CAAC,CAAC;IAC1B,CAAC,CAAC,OAAOL,GAAG,EAAE;MACZC,OAAO,CAACvC,KAAK,CAAC,yBAAyB,EAAEsC,GAAG,CAAC;MAC7CrC,QAAQ,CAACqC,GAAG,CAACa,MAAM,IAAI,yCAAyC,CAAC;IACnE;EACF,CAAC;EAED,MAAMgB,wBAAwB,GAAG,MAAOC,cAAc,IAAK;IACzD,IAAI,CAACC,MAAM,CAACC,OAAO,CAAC,oDAAoD,CAAC,EAAE;MACzE;IACF;IAEA,IAAI;MACF,MAAMlF,mBAAmB,CAACmF,kBAAkB,CAACH,cAAc,CAAC;MAC5D,MAAMzB,gBAAgB,CAAC,CAAC;IAC1B,CAAC,CAAC,OAAOL,GAAG,EAAE;MACZC,OAAO,CAACvC,KAAK,CAAC,4BAA4B,EAAEsC,GAAG,CAAC;MAChDrC,QAAQ,CAAC,4CAA4C,CAAC;IACxD;EACF,CAAC;;EAED;EACA,MAAMuE,uBAAuB,GAAGA,CAACX,IAAI,EAAEY,OAAO,GAAG,IAAI,KAAK;IACxD/C,oBAAoB,CAACmC,IAAI,CAAC;IAC1BjC,kBAAkB,CAAC6C,OAAO,CAAC;IAE3B,IAAIZ,IAAI,KAAK,MAAM,IAAIY,OAAO,EAAE;MAC9B3C,kBAAkB,CAAC;QACjBC,YAAY,EAAE0C,OAAO,CAAC1C,YAAY;QAClCC,WAAW,EAAEyC,OAAO,CAACzC,WAAW,IAAI,EAAE;QACtCC,YAAY,EAAEwC,OAAO,CAACxC,YAAY,IAAI,EAAE;QACxCC,aAAa,EAAEuC,OAAO,CAACvC,aAAa,IAAI,EAAE;QAC1CC,QAAQ,EAAEsC,OAAO,CAACtC,QAAQ,IAAI,SAAS;QACvCC,kBAAkB,EAAEqC,OAAO,CAACrC,kBAAkB,IAAI;MACpD,CAAC,CAAC;IACJ;IAEAZ,oBAAoB,CAAC,IAAI,CAAC;EAC5B,CAAC;EAED,MAAMkD,wBAAwB,GAAGA,CAAA,KAAM;IACrClD,oBAAoB,CAAC,KAAK,CAAC;IAC3BI,kBAAkB,CAAC,IAAI,CAAC;IACxBE,kBAAkB,CAAC;MACjBC,YAAY,EAAE,MAAM;MACpBC,WAAW,EAAE,EAAE;MACfC,YAAY,EAAE,EAAE;MAChBC,aAAa,EAAE,EAAE;MACjBC,QAAQ,EAAE,SAAS;MACnBC,kBAAkB,EAAE;IACtB,CAAC,CAAC;EACJ,CAAC;EAED,MAAMuC,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF,IAAIlD,iBAAiB,KAAK,MAAM,EAAE;QAChC,MAAMtC,cAAc,CAACyF,aAAa,CAACjD,eAAe,CAACkD,cAAc,EAAEhD,eAAe,CAAC;QACnF6C,wBAAwB,CAAC,CAAC;QAC1B,MAAM/B,gBAAgB,CAAC,CAAC,CAAC,CAAC;QAC1B,MAAMH,eAAe,CAAC,CAAC;MACzB;IACF,CAAC,CAAC,OAAOF,GAAG,EAAE;MACZC,OAAO,CAACvC,KAAK,CAAC,yBAAyB,EAAEsC,GAAG,CAAC;MAC7CrC,QAAQ,CAAC,sCAAsC,CAAC;IAClD;EACF,CAAC;EAED,MAAM6E,mBAAmB,GAAG,MAAOC,aAAa,IAAK;IACnD,IAAI,CAACV,MAAM,CAACC,OAAO,CAAC,+CAA+C,CAAC,EAAE;MACpE;IACF;IAEA,IAAI;MACF,MAAMnF,cAAc,CAAC6F,aAAa,CAACD,aAAa,CAAC;MACjD,MAAMpC,gBAAgB,CAAC,CAAC,CAAC,CAAC;MAC1B,MAAMH,eAAe,CAAC,CAAC;IACzB,CAAC,CAAC,OAAOF,GAAG,EAAE;MACZC,OAAO,CAACvC,KAAK,CAAC,4BAA4B,EAAEsC,GAAG,CAAC;MAChDrC,QAAQ,CAAC,yCAAyC,CAAC;IACrD;EACF,CAAC;EAED,MAAMgF,mBAAmB,GAAIC,IAAI,IAAK;IACpC,MAAMC,MAAM,GAAG;MACb,MAAM,EAAE,MAAM;MACd,uBAAuB,EAAE,gBAAgB;MACzC,qBAAqB,EAAE,cAAc;MACrC,gBAAgB,EAAE,gBAAgB;MAClC,SAAS,EAAE;IACb,CAAC;IACD,OAAOA,MAAM,CAACD,IAAI,CAAC,IAAIA,IAAI;EAC7B,CAAC;EAED,MAAME,aAAa,GAAIC,KAAK,IAAK;IAC/B,MAAMC,MAAM,GAAG;MACb,QAAQ,EAAE,SAAS;MACnB,WAAW,EAAE,SAAS;MACtB,UAAU,EAAE,SAAS;MACrB,YAAY,EAAE,SAAS;MACvB,WAAW,EAAE;IACf,CAAC;IACD,OAAOA,MAAM,CAACD,KAAK,CAAC,IAAI,SAAS;EACnC,CAAC;EAED,MAAME,gBAAgB,GAAIpD,QAAQ,IAAK;IACrC,MAAMmD,MAAM,GAAG;MACb,OAAO,EAAE,SAAS;MAClB,SAAS,EAAE,SAAS;MACpB,MAAM,EAAE,SAAS;MACjB,SAAS,EAAE;IACb,CAAC;IACD,OAAOA,MAAM,CAACnD,QAAQ,CAAC,IAAI,SAAS;EACtC,CAAC;EAED,IAAIrC,OAAO,EAAE;IACX,oBACEP,OAAA,CAACjD,GAAG;MAACkJ,OAAO,EAAC,MAAM;MAACC,cAAc,EAAC,QAAQ;MAACC,UAAU,EAAC,QAAQ;MAACC,SAAS,EAAC,OAAO;MAAAC,QAAA,eAC/ErG,OAAA,CAACrC,gBAAgB;QAAA2I,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CAAC;EAEV;EAEA,oBACEzG,OAAA,CAACjD,GAAG;IAAC2J,EAAE,EAAE;MAAEC,CAAC,EAAE;IAAE,CAAE;IAAAN,QAAA,gBAEhBrG,OAAA,CAACjD,GAAG;MAAC6J,EAAE,EAAE,CAAE;MAAAP,QAAA,eACTrG,OAAA,CAAChD,UAAU;QAAC6J,OAAO,EAAC,IAAI;QAACH,EAAE,EAAE;UAAEI,UAAU,EAAE,GAAG;UAAEC,KAAK,EAAE;QAAe,CAAE;QAAAV,QAAA,EACrEhG;MAAY;QAAAiG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,EAELhG,KAAK,iBACJT,OAAA,CAACtC,KAAK;MAACsJ,QAAQ,EAAC,OAAO;MAACN,EAAE,EAAE;QAAEE,EAAE,EAAE;MAAE,CAAE;MAAAP,QAAA,EACnC5F;IAAK;MAAA6F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR,EAGA9F,WAAW,iBACVX,OAAA,CAAC9C,KAAK;MAACwJ,EAAE,EAAE;QAAEC,CAAC,EAAE,CAAC;QAAEC,EAAE,EAAE,CAAC;QAAEK,OAAO,EAAE;MAAU,CAAE;MAAAZ,QAAA,eAC7CrG,OAAA,CAAC7B,KAAK;QAAC+I,SAAS,EAAC,KAAK;QAACC,OAAO,EAAE,CAAE;QAAChB,UAAU,EAAC,QAAQ;QAACD,cAAc,EAAC,eAAe;QAACkB,QAAQ,EAAC,MAAM;QAAAf,QAAA,gBAEnGrG,OAAA,CAAC7B,KAAK;UAAC+I,SAAS,EAAC,KAAK;UAACf,UAAU,EAAC,QAAQ;UAACgB,OAAO,EAAE,CAAE;UAAAd,QAAA,gBACpDrG,OAAA,CAACjB,UAAU;YAACgI,KAAK,EAAC,SAAS;YAACM,QAAQ,EAAC;UAAO;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/CzG,OAAA,CAACjD,GAAG;YAAAsJ,QAAA,gBACFrG,OAAA,CAAChD,UAAU;cAAC6J,OAAO,EAAC,IAAI;cAACC,UAAU,EAAC,MAAM;cAACJ,EAAE,EAAE;gBAAEY,UAAU,EAAE;cAAE,CAAE;cAAAjB,QAAA,EAC9D1F,WAAW,CAAC4G,mBAAmB,IAAI;YAAC;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC,eACbzG,OAAA,CAAChD,UAAU;cAAC6J,OAAO,EAAC,SAAS;cAACE,KAAK,EAAC,gBAAgB;cAAAV,QAAA,EAAC;YAErD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAGRzG,OAAA,CAAC7B,KAAK;UAAC+I,SAAS,EAAC,KAAK;UAACf,UAAU,EAAC,QAAQ;UAACgB,OAAO,EAAE,CAAE;UAAAd,QAAA,gBACpDrG,OAAA,CAACnB,UAAU;YAACkI,KAAK,EAAC,MAAM;YAACM,QAAQ,EAAC;UAAO;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5CzG,OAAA,CAACjD,GAAG;YAAAsJ,QAAA,gBACFrG,OAAA,CAAChD,UAAU;cAAC6J,OAAO,EAAC,IAAI;cAACC,UAAU,EAAC,MAAM;cAACJ,EAAE,EAAE;gBAAEY,UAAU,EAAE;cAAE,CAAE;cAAAjB,QAAA,EAC9D1F,WAAW,CAAC6G,cAAc,IAAI;YAAC;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC,eACbzG,OAAA,CAAChD,UAAU;cAAC6J,OAAO,EAAC,SAAS;cAACE,KAAK,EAAC,gBAAgB;cAAAV,QAAA,EAAC;YAErD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAGRzG,OAAA,CAAC7B,KAAK;UAAC+I,SAAS,EAAC,KAAK;UAACf,UAAU,EAAC,QAAQ;UAACgB,OAAO,EAAE,CAAE;UAAAd,QAAA,gBACpDrG,OAAA,CAACT,eAAe;YAACwH,KAAK,EAAC,SAAS;YAACM,QAAQ,EAAC;UAAO;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACpDzG,OAAA,CAACjD,GAAG;YAAAsJ,QAAA,gBACFrG,OAAA,CAAChD,UAAU;cAAC6J,OAAO,EAAC,IAAI;cAACC,UAAU,EAAC,MAAM;cAACJ,EAAE,EAAE;gBAAEY,UAAU,EAAE;cAAE,CAAE;cAAAjB,QAAA,EAC9D1F,WAAW,CAAC8G,gBAAgB,IAAI;YAAC;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC,eACbzG,OAAA,CAAChD,UAAU;cAAC6J,OAAO,EAAC,SAAS;cAACE,KAAK,EAAC,gBAAgB;cAAAV,QAAA,EAAC;YAErD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAGRzG,OAAA,CAAC7B,KAAK;UAAC+I,SAAS,EAAC,KAAK;UAACf,UAAU,EAAC,QAAQ;UAACgB,OAAO,EAAE,CAAE;UAAAd,QAAA,gBACpDrG,OAAA,CAACP,YAAY;YAACsH,KAAK,EAAC,SAAS;YAACM,QAAQ,EAAC;UAAO;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjDzG,OAAA,CAACjD,GAAG;YAAAsJ,QAAA,gBACFrG,OAAA,CAAChD,UAAU;cAAC6J,OAAO,EAAC,IAAI;cAACC,UAAU,EAAC,MAAM;cAACJ,EAAE,EAAE;gBAAEY,UAAU,EAAE;cAAE,CAAE;cAAAjB,QAAA,EAC9D1F,WAAW,CAAC+G,kBAAkB,IAAI;YAAC;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CAAC,eACbzG,OAAA,CAAChD,UAAU;cAAC6J,OAAO,EAAC,SAAS;cAACE,KAAK,EAAC,gBAAgB;cAAAV,QAAA,EAAC;YAErD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAGRzG,OAAA,CAAC7B,KAAK;UAAC+I,SAAS,EAAC,KAAK;UAACf,UAAU,EAAC,QAAQ;UAACgB,OAAO,EAAE,CAAE;UAAAd,QAAA,gBACpDrG,OAAA,CAACjD,GAAG;YAAC2J,EAAE,EAAE;cACPiB,KAAK,EAAE,EAAE;cACTC,MAAM,EAAE,EAAE;cACVC,YAAY,EAAE,KAAK;cACnBZ,OAAO,EAAGtG,WAAW,CAAC+G,kBAAkB,IAAI/G,WAAW,CAAC6G,cAAc,IAAI,CAAC,CAAC,IAAK,GAAG,GAAG,cAAc,GAC3F7G,WAAW,CAAC+G,kBAAkB,IAAI/G,WAAW,CAAC6G,cAAc,IAAI,CAAC,CAAC,IAAK,GAAG,GAAG,cAAc,GAAG,YAAY;cACpHvB,OAAO,EAAE,MAAM;cACfE,UAAU,EAAE,QAAQ;cACpBD,cAAc,EAAE;YAClB,CAAE;YAAAG,QAAA,eACArG,OAAA,CAAChD,UAAU;cAAC6J,OAAO,EAAC,SAAS;cAACC,UAAU,EAAC,MAAM;cAACC,KAAK,EAAC,OAAO;cAAAV,QAAA,GAC1D1F,WAAW,CAAC6G,cAAc,GAAG,CAAC,GAAGM,IAAI,CAACC,KAAK,CAAEpH,WAAW,CAAC+G,kBAAkB,GAAG/G,WAAW,CAAC6G,cAAc,GAAI,GAAG,CAAC,GAAG,CAAC,EAAC,GACxH;YAAA;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACNzG,OAAA,CAACjD,GAAG;YAAAsJ,QAAA,gBACFrG,OAAA,CAAChD,UAAU;cAAC6J,OAAO,EAAC,OAAO;cAACC,UAAU,EAAC,QAAQ;cAACJ,EAAE,EAAE;gBAAEY,UAAU,EAAE;cAAE,CAAE;cAAAjB,QAAA,EAAC;YAEvE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbzG,OAAA,CAAChD,UAAU;cAAC6J,OAAO,EAAC,SAAS;cAACE,KAAK,EAAC,gBAAgB;cAAAV,QAAA,GACjD1F,WAAW,CAACqH,cAAc,IAAI,CAAC,EAAC,SACnC;YAAA;cAAA1B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACR,eAGDzG,OAAA,CAACjD,GAAG;MAAAsJ,QAAA,eACFrG,OAAA,CAACjD,GAAG;QAAAsJ,QAAA,gBAEFrG,OAAA,CAACjD,GAAG;UAACkJ,OAAO,EAAC,MAAM;UAACC,cAAc,EAAC,eAAe;UAACC,UAAU,EAAC,QAAQ;UAACS,EAAE,EAAE,CAAE;UAAAP,QAAA,gBAC3ErG,OAAA,CAAChD,UAAU;YAAC6J,OAAO,EAAC,IAAI;YAACH,EAAE,EAAE;cAAEI,UAAU,EAAE,GAAG;cAAEC,KAAK,EAAE;YAAe,CAAE;YAAAV,QAAA,EAAC;UAEzE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbzG,OAAA,CAAC/C,MAAM;YACL4J,OAAO,EAAC,WAAW;YACnBoB,SAAS,eAAEjI,OAAA,CAACzB,OAAO;cAAA+H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvByB,OAAO,EAAEA,CAAA,KAAM7D,4BAA4B,CAAC,QAAQ,CAAE;YACtDqC,EAAE,EAAE;cACFyB,aAAa,EAAE,MAAM;cACrBrB,UAAU,EAAE,GAAG;cACfsB,EAAE,EAAE,CAAC;cACLC,EAAE,EAAE,CAAC;cACLC,eAAe,EAAE,SAAS;cAC1BvB,KAAK,EAAE,SAAS;cAChBwB,MAAM,EAAE,mBAAmB;cAC3BC,SAAS,EAAE,MAAM;cACjB,SAAS,EAAE;gBACTF,eAAe,EAAE,SAAS;gBAC1BE,SAAS,EAAE;cACb;YACF,CAAE;YAAAnC,QAAA,EACH;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EAELxF,mBAAmB,gBAClBjB,OAAA,CAACjD,GAAG;UAACkJ,OAAO,EAAC,MAAM;UAACC,cAAc,EAAC,QAAQ;UAACmC,EAAE,EAAE,CAAE;UAAAhC,QAAA,eAChDrG,OAAA,CAACrC,gBAAgB;YAAA2I,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,gBAENzG,OAAA,CAACjD,GAAG;UAAAsJ,QAAA,EACDtF,YAAY,CAAC0H,MAAM,KAAK,CAAC,gBACxBzI,OAAA,CAAC9C,KAAK;YACJwL,SAAS,EAAE,CAAE;YACbhC,EAAE,EAAE;cACFC,CAAC,EAAE,CAAC;cACJgC,SAAS,EAAE,QAAQ;cACnBL,eAAe,EAAE,SAAS;cAC1BC,MAAM,EAAE,YAAY;cACpBK,WAAW,EAAE;YACf,CAAE;YAAAvC,QAAA,gBAEFrG,OAAA,CAACjB,UAAU;cAAC2H,EAAE,EAAE;gBAAEW,QAAQ,EAAE,EAAE;gBAAEN,KAAK,EAAE,UAAU;gBAAEH,EAAE,EAAE;cAAE;YAAE;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9DzG,OAAA,CAAChD,UAAU;cAAC6J,OAAO,EAAC,IAAI;cAACE,KAAK,EAAC,gBAAgB;cAAC8B,YAAY;cAAAxC,QAAA,EAAC;YAE7D;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbzG,OAAA,CAAChD,UAAU;cAAC6J,OAAO,EAAC,OAAO;cAACE,KAAK,EAAC,gBAAgB;cAACL,EAAE,EAAE;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAP,QAAA,EAAC;YAElE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbzG,OAAA,CAAC/C,MAAM;cACL4J,OAAO,EAAC,WAAW;cACnBoB,SAAS,eAAEjI,OAAA,CAACzB,OAAO;gBAAA+H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACvByB,OAAO,EAAEA,CAAA,KAAM7D,4BAA4B,CAAC,QAAQ,CAAE;cACtDqC,EAAE,EAAE;gBACFyB,aAAa,EAAE,MAAM;gBACrBG,eAAe,EAAE,SAAS;gBAC1BvB,KAAK,EAAE,SAAS;gBAChBwB,MAAM,EAAE,mBAAmB;gBAC3BC,SAAS,EAAE,MAAM;gBACjB,SAAS,EAAE;kBACTF,eAAe,EAAE,SAAS;kBAC1BE,SAAS,EAAE;gBACb;cACF,CAAE;cAAAnC,QAAA,EACH;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,GAER1F,YAAY,CAAC+H,GAAG,CAAEpG,YAAY,iBAC5B1C,OAAA,CAAChC,SAAS;YAER0I,EAAE,EAAE;cACFE,EAAE,EAAE,CAAC;cACL,UAAU,EAAE;gBAAEX,OAAO,EAAE;cAAO,CAAC;cAC/BuC,SAAS,EAAE,2BAA2B;cACtCD,MAAM,EAAE,WAAW;cACnBK,WAAW,EAAE;YACf,CAAE;YAAAvC,QAAA,gBAEFrG,OAAA,CAAC/B,gBAAgB;cACf8K,UAAU,eAAE/I,OAAA,CAACX,cAAc;gBAAAiH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC/BC,EAAE,EAAE;gBACF,SAAS,EAAE;kBACT4B,eAAe,EAAE;gBACnB;cACF,CAAE;cAAAjC,QAAA,eAEFrG,OAAA,CAACjD,GAAG;gBAACkJ,OAAO,EAAC,MAAM;gBAACE,UAAU,EAAC,QAAQ;gBAACD,cAAc,EAAC,eAAe;gBAACyB,KAAK,EAAC,MAAM;gBAAAtB,QAAA,gBACjFrG,OAAA,CAACjD,GAAG;kBAACkJ,OAAO,EAAC,MAAM;kBAACE,UAAU,EAAC,QAAQ;kBAAC6C,GAAG,EAAE,CAAE;kBAAA3C,QAAA,gBAC7CrG,OAAA,CAACjB,UAAU;oBAACgI,KAAK,EAAC,SAAS;oBAACL,EAAE,EAAE;sBAAEW,QAAQ,EAAE;oBAAG;kBAAE;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACpDzG,OAAA,CAACjD,GAAG;oBAAAsJ,QAAA,gBACFrG,OAAA,CAAChD,UAAU;sBAAC6J,OAAO,EAAC,IAAI;sBAACH,EAAE,EAAE;wBAAEI,UAAU,EAAE;sBAAI,CAAE;sBAAAT,QAAA,EAC9C3D,YAAY,CAACb;oBAAiB;sBAAAyE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrB,CAAC,eACbzG,OAAA,CAACjD,GAAG;sBAACkJ,OAAO,EAAC,MAAM;sBAAC+C,GAAG,EAAE,CAAE;sBAACC,EAAE,EAAE,GAAI;sBAAA5C,QAAA,GACjC3D,YAAY,CAACZ,KAAK,iBACjB9B,OAAA,CAACjD,GAAG;wBAACkJ,OAAO,EAAC,MAAM;wBAACE,UAAU,EAAC,QAAQ;wBAAC6C,GAAG,EAAE,GAAI;wBAAA3C,QAAA,gBAC/CrG,OAAA,CAACf,SAAS;0BAACoI,QAAQ,EAAC,OAAO;0BAACN,KAAK,EAAC;wBAAQ;0BAAAT,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAC7CzG,OAAA,CAAChD,UAAU;0BAAC6J,OAAO,EAAC,OAAO;0BAACE,KAAK,EAAC,gBAAgB;0BAAAV,QAAA,EAC/C3D,YAAY,CAACZ;wBAAK;0BAAAwE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACT,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACV,CACN,EACA/D,YAAY,CAACX,QAAQ,iBACpB/B,OAAA,CAACjD,GAAG;wBAACkJ,OAAO,EAAC,MAAM;wBAACE,UAAU,EAAC,QAAQ;wBAAC6C,GAAG,EAAE,GAAI;wBAAA3C,QAAA,gBAC/CrG,OAAA,CAACb,SAAS;0BAACkI,QAAQ,EAAC,OAAO;0BAACN,KAAK,EAAC;wBAAQ;0BAAAT,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAC7CzG,OAAA,CAAChD,UAAU;0BAAC6J,OAAO,EAAC,OAAO;0BAACE,KAAK,EAAC,gBAAgB;0BAAAV,QAAA,EAC/C3D,YAAY,CAACX;wBAAQ;0BAAAuE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACZ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACV,CACN;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENzG,OAAA,CAACjD,GAAG;kBAACkJ,OAAO,EAAC,MAAM;kBAACE,UAAU,EAAC,QAAQ;kBAAC6C,GAAG,EAAE,CAAE;kBAACd,OAAO,EAAGgB,CAAC,IAAKA,CAAC,CAACC,eAAe,CAAC,CAAE;kBAAA9C,QAAA,gBAClFrG,OAAA,CAAC7C,IAAI;oBACHiM,IAAI,eAAEpJ,OAAA,CAACnB,UAAU;sBAAAyH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAE;oBACrB4C,KAAK,EAAE,GAAGnF,KAAK,CAACC,OAAO,CAAChD,sBAAsB,CAACuB,YAAY,CAAC0B,eAAe,CAAC,CAAC,GAAGjD,sBAAsB,CAACuB,YAAY,CAAC0B,eAAe,CAAC,CAACqE,MAAM,GAAG,CAAC,UAAW;oBAC1Ja,IAAI,EAAC,OAAO;oBACZvC,KAAK,EAAC,SAAS;oBACfF,OAAO,EAAC,UAAU;oBAClB0C,SAAS;oBACTrB,OAAO,EAAEA,CAAA,KAAM;sBACb;sBACApH,kBAAkB,CAAC,IAAI,CAAC;oBAC1B,CAAE;oBACF4F,EAAE,EAAE;sBACFI,UAAU,EAAE,GAAG;sBACfwB,eAAe,EAAE,yBAAyB;sBAC1CvB,KAAK,EAAE,SAAS;sBAChBwB,MAAM,EAAE,mCAAmC;sBAC3C,SAAS,EAAE;wBACTD,eAAe,EAAE,oCAAoC;wBACrDvB,KAAK,EAAE;sBACT;oBACF;kBAAE;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACFzG,OAAA,CAACpC,OAAO;oBAAC4L,KAAK,EAAC,uBAAuB;oBAAAnD,QAAA,eACpCrG,OAAA,CAAC5C,UAAU;sBACTkM,IAAI,EAAC,OAAO;sBACZpB,OAAO,EAAEA,CAAA,KAAM7D,4BAA4B,CAAC,MAAM,EAAE3B,YAAY,CAAE;sBAClEgE,EAAE,EAAE;wBACF,SAAS,EAAE;0BACT4B,eAAe,EAAE,eAAe;0BAChCvB,KAAK,EAAE;wBACT;sBACF,CAAE;sBAAAV,QAAA,eAEFrG,OAAA,CAACvB,QAAQ;wBAAC4I,QAAQ,EAAC;sBAAO;wBAAAf,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACVzG,OAAA,CAACpC,OAAO;oBAAC4L,KAAK,EAAC,sBAAsB;oBAAAnD,QAAA,eACnCrG,OAAA,CAAC5C,UAAU;sBACTkM,IAAI,EAAC,OAAO;sBACZpB,OAAO,EAAEA,CAAA,KAAMtD,wBAAwB,CAAClC,YAAY,CAAC0B,eAAe,CAAE;sBACtEsC,EAAE,EAAE;wBACF,SAAS,EAAE;0BACT4B,eAAe,EAAE,aAAa;0BAC9BvB,KAAK,EAAE;wBACT;sBACF,CAAE;sBAAAV,QAAA,eAEFrG,OAAA,CAACrB,UAAU;wBAAC0I,QAAQ,EAAC;sBAAO;wBAAAf,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACU,CAAC,eAEnBzG,OAAA,CAAC9B,gBAAgB;cAACwI,EAAE,EAAE;gBAAE+C,EAAE,EAAE;cAAE,CAAE;cAAApD,QAAA,gBAC9BrG,OAAA,CAAChD,UAAU;gBAAC6J,OAAO,EAAC,WAAW;gBAACgC,YAAY;gBAACnC,EAAE,EAAE;kBAAEI,UAAU,EAAE,GAAG;kBAAEF,EAAE,EAAE;gBAAE,CAAE;gBAAAP,QAAA,EAAC;cAE7E;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,EAEX,CAACvC,KAAK,CAACC,OAAO,CAAChD,sBAAsB,CAACuB,YAAY,CAAC0B,eAAe,CAAC,CAAC,IAAIjD,sBAAsB,CAACuB,YAAY,CAAC0B,eAAe,CAAC,CAACqE,MAAM,KAAK,CAAC,gBACzIzI,OAAA,CAACjD,GAAG;gBACF2J,EAAE,EAAE;kBACFC,CAAC,EAAE,CAAC;kBACJgC,SAAS,EAAE,QAAQ;kBACnBL,eAAe,EAAE,SAAS;kBAC1BT,YAAY,EAAE,CAAC;kBACfU,MAAM,EAAE,YAAY;kBACpBK,WAAW,EAAE;gBACf,CAAE;gBAAAvC,QAAA,eAEFrG,OAAA,CAAChD,UAAU;kBAAC6J,OAAO,EAAC,OAAO;kBAACE,KAAK,EAAC,gBAAgB;kBAAAV,QAAA,EAAC;gBAEnD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,gBAENzG,OAAA,CAACnC,IAAI;gBAAC6L,KAAK;gBAAArD,QAAA,EACRnC,KAAK,CAACC,OAAO,CAAChD,sBAAsB,CAACuB,YAAY,CAAC0B,eAAe,CAAC,CAAC,IAAIjD,sBAAsB,CAACuB,YAAY,CAAC0B,eAAe,CAAC,CAAC0E,GAAG,CAAE5D,OAAO,iBACvIlF,OAAA,CAAClC,QAAQ;kBAEP6L,OAAO;kBACPjD,EAAE,EAAE;oBACF,SAAS,EAAE;sBACT4B,eAAe,EAAE;oBACnB;kBACF,CAAE;kBAAAjC,QAAA,gBAEFrG,OAAA,CAACjC,YAAY;oBACX6L,OAAO,eACL5J,OAAA,CAACjD,GAAG;sBAACkJ,OAAO,EAAC,MAAM;sBAACE,UAAU,EAAC,QAAQ;sBAAC6C,GAAG,EAAE,CAAE;sBAAA3C,QAAA,gBAC7CrG,OAAA,CAAChD,UAAU;wBAAC6J,OAAO,EAAC,OAAO;wBAACC,UAAU,EAAC,MAAM;wBAAAT,QAAA,EAC1CnB,OAAO,CAACI;sBAAc;wBAAAgB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACb,CAAC,eACbzG,OAAA,CAAC7C,IAAI;wBACHkM,KAAK,EAAE3D,mBAAmB,CAACR,OAAO,CAAC1C,YAAY,CAAE;wBACjD8G,IAAI,EAAC,OAAO;wBACZzC,OAAO,EAAC;sBAAU;wBAAAP,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnB,CAAC,eACFzG,OAAA,CAAC7C,IAAI;wBACHkM,KAAK,EAAEnE,OAAO,CAACY,KAAK,IAAI,QAAS;wBACjCwD,IAAI,EAAC,OAAO;wBACZvC,KAAK,EAAElB,aAAa,CAACX,OAAO,CAACY,KAAK;sBAAE;wBAAAQ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrC,CAAC,EACDvB,OAAO,CAACtC,QAAQ,IAAIsC,OAAO,CAACtC,QAAQ,KAAK,SAAS,iBACjD5C,OAAA,CAAC7C,IAAI;wBACHkM,KAAK,EAAEnE,OAAO,CAACtC,QAAS;wBACxB0G,IAAI,EAAC,OAAO;wBACZvC,KAAK,EAAEf,gBAAgB,CAACd,OAAO,CAACtC,QAAQ,CAAE;wBAC1CiE,OAAO,EAAC;sBAAQ;wBAAAP,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjB,CACF;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CACN;oBACDoD,SAAS,eACP7J,OAAA,CAACjD,GAAG;sBAAAsJ,QAAA,gBACFrG,OAAA,CAAChD,UAAU;wBAAC6J,OAAO,EAAC,OAAO;wBAACE,KAAK,EAAC,eAAe;wBAAAV,QAAA,GAC9CnB,OAAO,CAACzC,WAAW,IAAI,qBAAqB,EAC5CyC,OAAO,CAAC4E,cAAc,IAAI,cAAc,IAAIC,IAAI,CAAC7E,OAAO,CAAC4E,cAAc,CAAC,CAACE,kBAAkB,CAAC,CAAC,EAAE;sBAAA;wBAAA1D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtF,CAAC,EACZvB,OAAO,CAAC+E,qBAAqB,GAAG,CAAC,iBAChCjK,OAAA,CAAChD,UAAU;wBAAC6J,OAAO,EAAC,SAAS;wBAACE,KAAK,EAAC,SAAS;wBAAAV,QAAA,GAC1CnB,OAAO,CAAC+E,qBAAqB,EAAC,iBAC/B,EAAC/E,OAAO,CAACgF,yBAAyB,IAAI,MAAMhF,OAAO,CAACgF,yBAAyB,CAACC,OAAO,CAAC,CAAC,CAAC,cAAc;sBAAA;wBAAA7D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC5F,CACb;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE;kBACN;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACFzG,OAAA,CAACjD,GAAG;oBAACkJ,OAAO,EAAC,MAAM;oBAAC+C,GAAG,EAAE,GAAI;oBAACoB,EAAE,EAAE,CAAE;oBAAA/D,QAAA,gBAClCrG,OAAA,CAACpC,OAAO;sBAAC4L,KAAK,EAAC,YAAY;sBAAAnD,QAAA,eACzBrG,OAAA,CAAC5C,UAAU;wBACTkM,IAAI,EAAC,OAAO;wBACZpB,OAAO,EAAEA,CAAA,KAAMjD,uBAAuB,CAAC,MAAM,EAAEC,OAAO,CAAE;wBACxDwB,EAAE,EAAE;0BACF,SAAS,EAAE;4BACT4B,eAAe,EAAE,SAAS;4BAC1BvB,KAAK,EAAE;0BACT;wBACF,CAAE;wBAAAV,QAAA,eAEFrG,OAAA,CAACL,QAAQ;0BAAC0H,QAAQ,EAAC;wBAAO;0BAAAf,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnB;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC,eACVzG,OAAA,CAACpC,OAAO;sBAAC4L,KAAK,EAAC,UAAU;sBAAAnD,QAAA,eACvBrG,OAAA,CAAC5C,UAAU;wBACTkM,IAAI,EAAC,OAAO;wBACZpB,OAAO,EAAEA,CAAA,KAAMjD,uBAAuB,CAAC,MAAM,EAAEC,OAAO,CAAE;wBACxDwB,EAAE,EAAE;0BACF,SAAS,EAAE;4BACT4B,eAAe,EAAE,SAAS;4BAC1BvB,KAAK,EAAE;0BACT;wBACF,CAAE;wBAAAV,QAAA,eAEFrG,OAAA,CAACvB,QAAQ;0BAAC4I,QAAQ,EAAC;wBAAO;0BAAAf,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnB;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC,eACVzG,OAAA,CAACpC,OAAO;sBAAC4L,KAAK,EAAC,SAAS;sBAAAnD,QAAA,eACtBrG,OAAA,CAAC5C,UAAU;wBACTkM,IAAI,EAAC,OAAO;wBACZpB,OAAO,EAAEA,CAAA,KAAM3C,mBAAmB,CAACL,OAAO,CAACI,cAAc,CAAE;wBAC3DoB,EAAE,EAAE;0BACF,SAAS,EAAE;4BACT4B,eAAe,EAAE,YAAY;4BAC7BvB,KAAK,EAAE;0BACT;wBACF,CAAE;wBAAAV,QAAA,eAEFrG,OAAA,CAACrB,UAAU;0BAAC0I,QAAQ,EAAC;wBAAO;0BAAAf,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrB;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CAAC;gBAAA,GA5FDvB,OAAO,CAACI,cAAc;kBAAAgB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA6FnB,CACX;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CACP;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACe,CAAC;UAAA,GA7Nd/D,YAAY,CAAC0B,eAAe;YAAAkC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA8NxB,CACZ;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNzG,OAAA,CAAC3C,MAAM;MACLgN,IAAI,EAAEhJ,sBAAuB;MAC7BiJ,OAAO,EAAE/F,6BAA8B;MACvCgG,QAAQ,EAAC,IAAI;MACbC,SAAS;MACTC,UAAU,EAAE;QACV/D,EAAE,EAAE;UAAEmB,YAAY,EAAE;QAAE;MACxB,CAAE;MAAAxB,QAAA,gBAEFrG,OAAA,CAAC1C,WAAW;QAACoJ,EAAE,EAAE;UAAEgE,EAAE,EAAE;QAAE,CAAE;QAAArE,QAAA,eACzBrG,OAAA,CAAChD,UAAU;UAAC6J,OAAO,EAAC,IAAI;UAACH,EAAE,EAAE;YAAEI,UAAU,EAAE;UAAI,CAAE;UAAAT,QAAA,EAC9C9E,sBAAsB,KAAK,QAAQ,GAAG,wBAAwB,GAAG;QAAuB;UAAA+E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/E;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACdzG,OAAA,CAACzC,aAAa;QAAA8I,QAAA,eACZrG,OAAA,CAACjD,GAAG;UAAC2J,EAAE,EAAE;YAAE+C,EAAE,EAAE;UAAE,CAAE;UAAApD,QAAA,gBACjBrG,OAAA,CAACvC,SAAS;YACR+M,SAAS;YACTnB,KAAK,EAAC,mBAAmB;YACzBsB,KAAK,EAAEhJ,oBAAoB,CAACE,iBAAkB;YAC9C+I,QAAQ,EAAG1B,CAAC,IAAKtH,uBAAuB,CAAC;cAAE,GAAGD,oBAAoB;cAAEE,iBAAiB,EAAEqH,CAAC,CAAC2B,MAAM,CAACF;YAAM,CAAC,CAAE;YACzGG,MAAM,EAAC,QAAQ;YACfC,QAAQ;YACRlE,OAAO,EAAC,UAAU;YAClBH,EAAE,EAAE;cAAEE,EAAE,EAAE;YAAE;UAAE;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eAEFzG,OAAA,CAACvC,SAAS;YACR+M,SAAS;YACTnB,KAAK,EAAC,OAAO;YACb2B,IAAI,EAAC,OAAO;YACZL,KAAK,EAAEhJ,oBAAoB,CAACG,KAAM;YAClC8I,QAAQ,EAAG1B,CAAC,IAAKtH,uBAAuB,CAAC;cAAE,GAAGD,oBAAoB;cAAEG,KAAK,EAAEoH,CAAC,CAAC2B,MAAM,CAACF;YAAM,CAAC,CAAE;YAC7FG,MAAM,EAAC,QAAQ;YACfjE,OAAO,EAAC,UAAU;YAClBoE,UAAU,EAAC,uDAAuD;YAClEvE,EAAE,EAAE;cAAEE,EAAE,EAAE;YAAE;UAAE;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eAEFzG,OAAA,CAACvC,SAAS;YACR+M,SAAS;YACTnB,KAAK,EAAC,UAAU;YAChBsB,KAAK,EAAEhJ,oBAAoB,CAACI,QAAS;YACrC6I,QAAQ,EAAG1B,CAAC,IAAKtH,uBAAuB,CAAC;cAAE,GAAGD,oBAAoB;cAAEI,QAAQ,EAAEmH,CAAC,CAAC2B,MAAM,CAACF;YAAM,CAAC,CAAE;YAChGG,MAAM,EAAC,QAAQ;YACfjE,OAAO,EAAC,UAAU;YAClBoE,UAAU,EAAC;UAA+C;YAAA3E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAChBzG,OAAA,CAACxC,aAAa;QAACkJ,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAE8C,EAAE,EAAE;QAAE,CAAE;QAAApD,QAAA,gBACjCrG,OAAA,CAAC/C,MAAM;UACLiL,OAAO,EAAE3D,6BAA8B;UACvCmC,EAAE,EAAE;YAAEyB,aAAa,EAAE;UAAO,CAAE;UAAA9B,QAAA,EAC/B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTzG,OAAA,CAAC/C,MAAM;UACLiL,OAAO,EAAE1D,wBAAyB;UAClCqC,OAAO,EAAC,WAAW;UACnBH,EAAE,EAAE;YACFyB,aAAa,EAAE,MAAM;YACrBrB,UAAU,EAAE,GAAG;YACfsB,EAAE,EAAE;UACN,CAAE;UAAA/B,QAAA,EAED9E,sBAAsB,KAAK,QAAQ,GAAG,MAAM,GAAG;QAAO;UAAA+E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGTzG,OAAA,CAAC3C,MAAM;MACLgN,IAAI,EAAErI,iBAAkB;MACxBsI,OAAO,EAAEnF,wBAAyB;MAClCoF,QAAQ,EAAC,IAAI;MACbC,SAAS;MACTC,UAAU,EAAE;QACV/D,EAAE,EAAE;UAAEmB,YAAY,EAAE;QAAE;MACxB,CAAE;MAAAxB,QAAA,gBAEFrG,OAAA,CAAC1C,WAAW;QAACoJ,EAAE,EAAE;UAAEgE,EAAE,EAAE;QAAE,CAAE;QAAArE,QAAA,eACzBrG,OAAA,CAAChD,UAAU;UAAC6J,OAAO,EAAC,IAAI;UAACH,EAAE,EAAE;YAAEI,UAAU,EAAE;UAAI,CAAE;UAAAT,QAAA,EAC9CnE,iBAAiB,KAAK,MAAM,GAAG,kBAAkB,GAAG;QAAkB;UAAAoE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACdzG,OAAA,CAACzC,aAAa;QAAA8I,QAAA,eACZrG,OAAA,CAACjD,GAAG;UAAC2J,EAAE,EAAE;YAAE+C,EAAE,EAAE;UAAE,CAAE;UAAApD,QAAA,EAChBnE,iBAAiB,KAAK,MAAM,IAAIE,eAAe,gBAC9CpC,OAAA,CAACnC,IAAI;YAAAwI,QAAA,gBACHrG,OAAA,CAAClC,QAAQ;cAAAuI,QAAA,eACPrG,OAAA,CAACjC,YAAY;gBACX6L,OAAO,EAAC,gBAAgB;gBACxBC,SAAS,EAAEzH,eAAe,CAACkD;cAAe;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC,eACXzG,OAAA,CAAC3B,OAAO;cAAAiI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACXzG,OAAA,CAAClC,QAAQ;cAAAuI,QAAA,eACPrG,OAAA,CAACjC,YAAY;gBACX6L,OAAO,EAAC,MAAM;gBACdC,SAAS,EAAEnE,mBAAmB,CAACtD,eAAe,CAACI,YAAY;cAAE;gBAAA8D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9D;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC,eACXzG,OAAA,CAAC3B,OAAO;cAAAiI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACXzG,OAAA,CAAClC,QAAQ;cAAAuI,QAAA,eACPrG,OAAA,CAACjC,YAAY;gBACX6L,OAAO,EAAC,OAAO;gBACfC,SAAS,eACP7J,OAAA,CAAC7C,IAAI;kBACHkM,KAAK,EAAEjH,eAAe,CAAC0D,KAAM;kBAC7BiB,KAAK,EAAElB,aAAa,CAACzD,eAAe,CAAC0D,KAAK,CAAE;kBAC5CwD,IAAI,EAAC;gBAAO;kBAAAhD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb;cACF;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC,eACXzG,OAAA,CAAC3B,OAAO;cAAAiI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACXzG,OAAA,CAAClC,QAAQ;cAAAuI,QAAA,eACPrG,OAAA,CAACjC,YAAY;gBACX6L,OAAO,EAAC,aAAa;gBACrBC,SAAS,EAAEzH,eAAe,CAACK,WAAW,IAAI;cAAsB;gBAAA6D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC,eACXzG,OAAA,CAAC3B,OAAO;cAAAiI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACXzG,OAAA,CAAClC,QAAQ;cAAAuI,QAAA,eACPrG,OAAA,CAACjC,YAAY;gBACX6L,OAAO,EAAC,aAAU;gBAClBC,SAAS,eACP7J,OAAA,CAAC7C,IAAI;kBACHkM,KAAK,EAAEjH,eAAe,CAACQ,QAAQ,IAAI,SAAU;kBAC7CmE,KAAK,EAAEf,gBAAgB,CAAC5D,eAAe,CAACQ,QAAQ,IAAI,SAAS,CAAE;kBAC/D0G,IAAI,EAAC;gBAAO;kBAAAhD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb;cACF;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC,eACXzG,OAAA,CAAC3B,OAAO;cAAAiI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACXzG,OAAA,CAAClC,QAAQ;cAAAuI,QAAA,eACPrG,OAAA,CAACjC,YAAY;gBACX6L,OAAO,EAAC,cAAc;gBACtBC,SAAS,EAAEzH,eAAe,CAACM,YAAY,IAAI;cAAgB;gBAAA4D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5D;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC,EACVrE,eAAe,CAACS,kBAAkB,iBACjC7C,OAAA,CAAAE,SAAA;cAAAmG,QAAA,gBACErG,OAAA,CAAC3B,OAAO;gBAAAiI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACXzG,OAAA,CAAClC,QAAQ;gBAAAuI,QAAA,eACPrG,OAAA,CAACjC,YAAY;kBACX6L,OAAO,EAAC,oBAAoB;kBAC5BC,SAAS,EAAEzH,eAAe,CAACS;gBAAmB;kBAAAyD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/C;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACM,CAAC;YAAA,eACX,CACH,eACDzG,OAAA,CAAC3B,OAAO;cAAAiI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACXzG,OAAA,CAAClC,QAAQ;cAAAuI,QAAA,eACPrG,OAAA,CAACjC,YAAY;gBACX6L,OAAO,EAAC,gBAAgB;gBACxBC,SAAS,EAAE,IAAIE,IAAI,CAAC3H,eAAe,CAAC0H,cAAc,CAAC,CAACE,kBAAkB,CAAC,OAAO;cAAE;gBAAA1D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC,EACVrE,eAAe,CAACO,aAAa,iBAC5B3C,OAAA,CAAAE,SAAA;cAAAmG,QAAA,gBACErG,OAAA,CAAC3B,OAAO;gBAAAiI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACXzG,OAAA,CAAClC,QAAQ;gBAAAuI,QAAA,eACPrG,OAAA,CAACjC,YAAY;kBACX6L,OAAO,EAAC,eAAe;kBACvBC,SAAS,EAAE,IAAIE,IAAI,CAAC3H,eAAe,CAACO,aAAa,CAAC,CAACqH,kBAAkB,CAAC,OAAO;gBAAE;kBAAA1D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACM,CAAC;YAAA,eACX,CACH,eACDzG,OAAA,CAAC3B,OAAO;cAAAiI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACXzG,OAAA,CAAClC,QAAQ;cAAAuI,QAAA,eACPrG,OAAA,CAACjC,YAAY;gBACX6L,OAAO,EAAC,gBAAgB;gBACxBC,SAAS,EAAEzH,eAAe,CAAC6H,qBAAqB,IAAI;cAAE;gBAAA3D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC,eACXzG,OAAA,CAAC3B,OAAO;cAAAiI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACXzG,OAAA,CAAClC,QAAQ;cAAAuI,QAAA,eACPrG,OAAA,CAACjC,YAAY;gBACX6L,OAAO,EAAC,eAAe;gBACvBC,SAAS,EAAE,GAAG,CAACzH,eAAe,CAAC8H,yBAAyB,IAAI,CAAC,EAAEC,OAAO,CAAC,CAAC,CAAC;cAAI;gBAAA7D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9E;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CAAC,gBAEPzG,OAAA,CAAAE,SAAA;YAAAmG,QAAA,gBACErG,OAAA,CAACvC,SAAS;cACR+M,SAAS;cACTU,MAAM;cACN7B,KAAK,EAAC,cAAc;cACpBsB,KAAK,EAAErI,eAAe,CAACE,YAAa;cACpCoI,QAAQ,EAAG1B,CAAC,IAAK3G,kBAAkB,CAAC;gBAAE,GAAGD,eAAe;gBAAEE,YAAY,EAAE0G,CAAC,CAAC2B,MAAM,CAACF;cAAM,CAAC,CAAE;cAC1FG,MAAM,EAAC,QAAQ;cACfpE,EAAE,EAAE;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAP,QAAA,gBAEdrG,OAAA,CAAC5B,QAAQ;gBAACuM,KAAK,EAAC,MAAM;gBAAAtE,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACtCzG,OAAA,CAAC5B,QAAQ;gBAACuM,KAAK,EAAC,uBAAuB;gBAAAtE,QAAA,EAAC;cAAqB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACxEzG,OAAA,CAAC5B,QAAQ;gBAACuM,KAAK,EAAC,qBAAqB;gBAAAtE,QAAA,EAAC;cAAmB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACpEzG,OAAA,CAAC5B,QAAQ;gBAACuM,KAAK,EAAC,gBAAgB;gBAAAtE,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAC1DzG,OAAA,CAAC5B,QAAQ;gBAACuM,KAAK,EAAC,SAAS;gBAAAtE,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC,eAEZzG,OAAA,CAACvC,SAAS;cACR+M,SAAS;cACTU,MAAM;cACN7B,KAAK,EAAC,aAAU;cAChBsB,KAAK,EAAErI,eAAe,CAACM,QAAS;cAChCgI,QAAQ,EAAG1B,CAAC,IAAK3G,kBAAkB,CAAC;gBAAE,GAAGD,eAAe;gBAAEM,QAAQ,EAAEsG,CAAC,CAAC2B,MAAM,CAACF;cAAM,CAAC,CAAE;cACtFG,MAAM,EAAC,QAAQ;cACfpE,EAAE,EAAE;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAP,QAAA,gBAEdrG,OAAA,CAAC5B,QAAQ;gBAACuM,KAAK,EAAC,OAAO;gBAAAtE,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACxCzG,OAAA,CAAC5B,QAAQ;gBAACuM,KAAK,EAAC,SAAS;gBAAAtE,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAC5CzG,OAAA,CAAC5B,QAAQ;gBAACuM,KAAK,EAAC,MAAM;gBAAAtE,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACtCzG,OAAA,CAAC5B,QAAQ;gBAACuM,KAAK,EAAC,SAAS;gBAAAtE,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC,eAEZzG,OAAA,CAACvC,SAAS;cACR+M,SAAS;cACTnB,KAAK,EAAC,aAAa;cACnBsB,KAAK,EAAErI,eAAe,CAACG,WAAY;cACnCmI,QAAQ,EAAG1B,CAAC,IAAK3G,kBAAkB,CAAC;gBAAE,GAAGD,eAAe;gBAAEG,WAAW,EAAEyG,CAAC,CAAC2B,MAAM,CAACF;cAAM,CAAC,CAAE;cACzFG,MAAM,EAAC,QAAQ;cACfK,SAAS;cACTC,IAAI,EAAE,CAAE;cACR1E,EAAE,EAAE;gBAAEE,EAAE,EAAE;cAAE;YAAE;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,eAEFzG,OAAA,CAACvC,SAAS;cACR+M,SAAS;cACTnB,KAAK,EAAC,cAAc;cACpBsB,KAAK,EAAErI,eAAe,CAACI,YAAa;cACpCkI,QAAQ,EAAG1B,CAAC,IAAK3G,kBAAkB,CAAC;gBAAE,GAAGD,eAAe;gBAAEI,YAAY,EAAEwG,CAAC,CAAC2B,MAAM,CAACF;cAAM,CAAC,CAAE;cAC1FG,MAAM,EAAC,QAAQ;cACfC,QAAQ;cACRE,UAAU,EAAC,0CAAuC;cAClDvE,EAAE,EAAE;gBAAEE,EAAE,EAAE;cAAE;YAAE;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,eAEFzG,OAAA,CAACvC,SAAS;cACR+M,SAAS;cACTnB,KAAK,EAAC,oBAAoB;cAC1BsB,KAAK,EAAErI,eAAe,CAACO,kBAAmB;cAC1C+H,QAAQ,EAAG1B,CAAC,IAAK3G,kBAAkB,CAAC;gBAAE,GAAGD,eAAe;gBAAEO,kBAAkB,EAAEqG,CAAC,CAAC2B,MAAM,CAACF;cAAM,CAAC,CAAE;cAChGG,MAAM,EAAC,QAAQ;cACfK,SAAS;cACTC,IAAI,EAAE,CAAE;cACRH,UAAU,EAAC,2CAA2C;cACtDvE,EAAE,EAAE;gBAAEE,EAAE,EAAE;cAAE;YAAE;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,eAEFzG,OAAA,CAACvC,SAAS;cACR+M,SAAS;cACTnB,KAAK,EAAC,eAAe;cACrB2B,IAAI,EAAC,MAAM;cACXL,KAAK,EAAErI,eAAe,CAACK,aAAc;cACrCiI,QAAQ,EAAG1B,CAAC,IAAK3G,kBAAkB,CAAC;gBAAE,GAAGD,eAAe;gBAAEK,aAAa,EAAEuG,CAAC,CAAC2B,MAAM,CAACF;cAAM,CAAC,CAAE;cAC3FG,MAAM,EAAC,QAAQ;cACfO,eAAe,EAAE;gBACfC,MAAM,EAAE;cACV;YAAE;cAAAhF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA,eACF;QACH;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAChBzG,OAAA,CAACxC,aAAa;QAACkJ,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAE8C,EAAE,EAAE;QAAE,CAAE;QAAApD,QAAA,gBACjCrG,OAAA,CAAC/C,MAAM;UACLiL,OAAO,EAAE/C,wBAAyB;UAClCuB,EAAE,EAAE;YAAEyB,aAAa,EAAE;UAAO,CAAE;UAAA9B,QAAA,EAE7BnE,iBAAiB,KAAK,MAAM,GAAG,QAAQ,GAAG;QAAS;UAAAoE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C,CAAC,EACRvE,iBAAiB,KAAK,MAAM,iBAC3BlC,OAAA,CAAC/C,MAAM;UACLiL,OAAO,EAAE9C,mBAAoB;UAC7ByB,OAAO,EAAC,WAAW;UACnBH,EAAE,EAAE;YACFyB,aAAa,EAAE,MAAM;YACrBrB,UAAU,EAAE,GAAG;YACfsB,EAAE,EAAE;UACN,CAAE;UAAA/B,QAAA,EACH;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGTzG,OAAA,CAACF,kBAAkB;MACjBM,UAAU,EAAEA,UAAW;MACvBiK,IAAI,EAAExJ,eAAgB;MACtByJ,OAAO,EAAEA,CAAA,KAAMxJ,kBAAkB,CAAC,KAAK,CAAE;MACzCyK,SAAS,EAAEA,CAAA,KAAM;QACfzI,WAAW,CAAC,CAAC;QACbG,eAAe,CAAC,CAAC;QACjBG,gBAAgB,CAAC,CAAC;QAClBtC,kBAAkB,CAAC,KAAK,CAAC;MAC3B;IAAE;MAAAwF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAACnG,EAAA,CAp+BIH,wBAAwB;AAAAqL,EAAA,GAAxBrL,wBAAwB;AAs+B9B,eAAeA,wBAAwB;AAAC,IAAAqL,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}