import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Paper,
  Button,
  IconButton,
  Alert,
  CircularProgress
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  Refresh as RefreshIcon,
  Cable as CableIcon,
  Assignment as AssignmentIcon
} from '@mui/icons-material';
import { useAuth } from '../../context/AuthContext';
import cantieriService from '../../services/cantieriService';
import AdminHomeButton from '../../components/common/AdminHomeButton';

const CantierePage = () => {
  const { cantiereId } = useParams();
  const { isImpersonating, selectCantiere } = useAuth();
  const navigate = useNavigate();

  const [cantiere, setCantiere] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Carica i dettagli del cantiere
  useEffect(() => {
    const fetchCantiere = async () => {
      try {
        setLoading(true);
        const data = await cantieriService.getCantiere(cantiereId);
        setCantiere(data);

        // Salva il cantiere selezionato nel contesto di autenticazione e nel localStorage
        selectCantiere(data);
        console.log('Cantiere selezionato:', data);
      } catch (err) {
        console.error('Errore nel caricamento del cantiere:', err);
        setError('Impossibile caricare i dettagli del cantiere. Riprova più tardi.');
      } finally {
        setLoading(false);
      }
    };

    if (cantiereId) {
      fetchCantiere();
    }
  }, [cantiereId]);

  // Torna alla lista dei cantieri
  const handleBackToCantieri = () => {
    navigate('/dashboard/cantieri');
  };

  // Naviga alla gestione cavi
  const navigateToGestioneCavi = () => {
    navigate('/dashboard/cavi/visualizza');
  };

  // Naviga alle certificazioni
  const navigateToCertificazioni = () => {
    navigate(`/dashboard/cantieri/${cantiereId}/certificazioni`);
  };

  // Naviga alle comande
  const navigateToComande = () => {
    navigate(`/cantieri/${cantiereId}/comande`);
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ mt: 2 }}>
        <Alert severity="error">{error}</Alert>
        <Button
          variant="contained"
          onClick={handleBackToCantieri}
          sx={{ mt: 2 }}
        >
          Torna alla lista cantieri
        </Button>
      </Box>
    );
  }

  if (!cantiere) {
    return (
      <Box sx={{ mt: 2 }}>
        <Alert severity="warning">Cantiere non trovato</Alert>
        <Button
          variant="contained"
          onClick={handleBackToCantieri}
          sx={{ mt: 2 }}
        >
          Torna alla lista cantieri
        </Button>
      </Box>
    );
  }

  return (
    <Box>
      <Box sx={{ mb: 3, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <IconButton onClick={handleBackToCantieri} sx={{ mr: 1 }}>
            <ArrowBackIcon />
          </IconButton>
          <Typography variant="h4">
            Dettagli Cantiere
          </Typography>
          <IconButton
            onClick={() => window.location.reload()}
            sx={{ ml: 2 }}
            color="primary"
            title="Ricarica la pagina"
          >
            <RefreshIcon />
          </IconButton>
        </Box>
        <AdminHomeButton />
      </Box>

      {/* Dettagli cantiere */}
      <Paper sx={{ mb: 3, p: 3 }}>
        <Typography variant="h5" gutterBottom>
          {cantiere.nome}
        </Typography>
        <Typography variant="body1" sx={{ mb: 1 }}>
          <strong>ID:</strong> {cantiere.id_cantiere}
        </Typography>
        <Typography variant="body1" sx={{ mb: 1 }}>
          <strong>Codice Univoco:</strong> {cantiere.codice_univoco}
        </Typography>
        <Typography variant="body1" sx={{ mb: 1 }}>
          <strong>Data Creazione:</strong> {new Date(cantiere.data_creazione).toLocaleString()}
        </Typography>
        {cantiere.descrizione && (
          <Typography variant="body1" sx={{ mb: 1 }}>
            <strong>Descrizione:</strong> {cantiere.descrizione}
          </Typography>
        )}
      </Paper>

      {/* Pulsanti di navigazione */}
      <Box sx={{ display: 'flex', gap: 2, mb: 3, flexWrap: 'wrap' }}>
        <Button
          variant="contained"
          color="primary"
          startIcon={<ArrowBackIcon />}
          onClick={handleBackToCantieri}
        >
          Torna alla Lista Cantieri
        </Button>

        <Button
          variant="contained"
          color="primary"
          startIcon={<CableIcon />}
          onClick={navigateToGestioneCavi}
        >
          Gestione Cavi
        </Button>

        <Button
          variant="contained"
          color="secondary"
          startIcon={<AssignmentIcon />}
          onClick={navigateToCertificazioni}
        >
          Certificazioni Cavi
        </Button>

        <Button
          variant="contained"
          color="info"
          startIcon={<AssignmentIcon />}
          onClick={navigateToComande}
        >
          Gestione Comande
        </Button>
      </Box>

      <Paper sx={{ p: 3, textAlign: 'center' }}>
        <Typography variant="h6" gutterBottom>
          Gestione Cantiere
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          Seleziona "Gestione Cavi" per visualizzare, aggiungere, modificare e gestire tutti i cavi del cantiere
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          Seleziona "Certificazioni Cavi" per gestire le certificazioni e gli strumenti di misura
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Seleziona "Gestione Comande" per creare e gestire ordini di lavoro per posa e collegamenti
        </Typography>
      </Paper>
    </Box>
  );
};

export default CantierePage;
