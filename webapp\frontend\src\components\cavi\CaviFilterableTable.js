import React, { useState, useEffect } from 'react';
import { Box, Typography, Chip, TableRow, TableCell, Checkbox, Button } from '@mui/material';
import {
  CheckBox as CheckBoxIcon,
  Clear as ClearIcon,
  Straighten as RulerIcon,
  Settings as SettingsIcon,
  PlayArrow as StartIcon
} from '@mui/icons-material';
import FilterableTable from '../common/FilterableTable';
import SmartCaviFilter from './SmartCaviFilter';
import ContextMenu from '../common/ContextMenu';
import useContextMenu from '../../hooks/useContextMenu';
import { formatDate } from '../../utils/dateUtils';

/**
 * Componente per visualizzare la lista dei cavi con filtri in stile Excel
 *
 * @param {Object} props - Proprietà del componente
 * @param {Array} props.cavi - Lista dei cavi da visualizzare
 * @param {boolean} props.loading - Indica se i dati sono in caricamento
 * @param {Function} props.onFilteredDataChange - Funzione chiamata quando i dati filtrati cambiano
 * @param {string} props.revisioneCorrente - Revisione corrente da mostrare nelle statistiche
 * @param {boolean} props.selectionEnabled - Abilita la selezione dei cavi
 * @param {Array} props.selectedCavi - Array degli ID dei cavi selezionati
 * @param {Function} props.onSelectionChange - Funzione chiamata quando cambia la selezione
 * @param {Array} props.contextMenuItems - Array di elementi per il menu contestuale
 * @param {Function} props.onContextMenuAction - Funzione chiamata quando si clicca su un elemento del menu contestuale
 * @param {Function} props.onStatusAction - Funzione chiamata quando si clicca sul pulsante stato
 */
const CaviFilterableTable = ({
  cavi = [],
  loading = false,
  onFilteredDataChange = null,
  revisioneCorrente = null,
  selectionEnabled = false,
  selectedCavi = [],
  onSelectionChange = null,
  onSelectionToggle = null,
  contextMenuItems = [],
  onContextMenuAction = null,
  onStatusAction = null
}) => {
  const [filteredCavi, setFilteredCavi] = useState(cavi);
  const [smartFilteredCavi, setSmartFilteredCavi] = useState(cavi);

  // Hook per il menu contestuale
  const { contextMenu, handleContextMenu, closeContextMenu } = useContextMenu();

  // Aggiorna i dati filtrati quando cambiano i cavi
  useEffect(() => {
    setFilteredCavi(cavi);
    setSmartFilteredCavi(cavi);
  }, [cavi]);

  // Notifica il componente padre quando cambiano i dati filtrati
  const handleFilteredDataChange = (data) => {
    setFilteredCavi(data);
    if (onFilteredDataChange) {
      onFilteredDataChange(data);
    }
  };

  // Gestisce il cambio dei dati dal filtro intelligente
  const handleSmartFilterChange = (data) => {
    console.log('CaviFilterableTable - Smart filter change:', {
      originalCount: cavi.length,
      filteredCount: data.length,
      filteredIds: data.map(c => c.id_cavo)
    });
    setSmartFilteredCavi(data);
    // Il filtro intelligente ha la priorità sui filtri Excel-like
    setFilteredCavi(data);
    if (onFilteredDataChange) {
      onFilteredDataChange(data);
    }
  };

  // Gestisce la selezione di un singolo cavo
  const handleCavoToggle = (cavoId) => {
    if (!selectionEnabled || !onSelectionChange) return;

    const isSelected = selectedCavi.includes(cavoId);
    let newSelection;

    if (isSelected) {
      // Rimuovi dalla selezione
      newSelection = selectedCavi.filter(id => id !== cavoId);
      console.log(`Cavo ${cavoId} deselezionato`);
    } else {
      // Aggiungi alla selezione
      newSelection = [...selectedCavi, cavoId];
      console.log(`Cavo ${cavoId} selezionato`);
    }

    onSelectionChange(newSelection);

    // Feedback visivo rapido (opzionale - può essere rimosso se troppo invasivo)
    // Potresti aggiungere qui un piccolo toast o animazione
  };

  // Seleziona tutti i cavi visibili (filtrati)
  const handleSelectAll = () => {
    if (!selectionEnabled || !onSelectionChange) return;

    const visibleCaviIds = filteredCavi.map(cavo => cavo.id_cavo);
    const allSelected = visibleCaviIds.every(id => selectedCavi.includes(id));

    if (allSelected) {
      // Deseleziona tutti i cavi visibili
      const newSelection = selectedCavi.filter(id => !visibleCaviIds.includes(id));
      onSelectionChange(newSelection);
    } else {
      // Seleziona tutti i cavi visibili
      const newSelection = [...new Set([...selectedCavi, ...visibleCaviIds])];
      onSelectionChange(newSelection);
    }
  };

  // Deseleziona tutti i cavi
  const handleClearSelection = () => {
    if (!selectionEnabled || !onSelectionChange) return;
    onSelectionChange([]);
  };



  // Definizione delle colonne
  const columns = [
    // Colonna di selezione (solo se abilitata)
    ...(selectionEnabled ? [{
      field: 'selection',
      headerName: '',
      disableFilter: true,
      disableSort: true,
      width: 50,
      align: 'center',
      headerStyle: { width: '50px', padding: '4px' },
      cellStyle: { width: '50px', padding: '4px', textAlign: 'center' },
      renderHeader: () => {
        const visibleCaviIds = filteredCavi.map(cavo => cavo.id_cavo);
        const allSelected = visibleCaviIds.length > 0 && visibleCaviIds.every(id => selectedCavi.includes(id));
        const someSelected = visibleCaviIds.some(id => selectedCavi.includes(id));

        return (
          <Checkbox
            checked={allSelected}
            indeterminate={someSelected && !allSelected}
            onChange={handleSelectAll}
            size="small"
            title={allSelected ? "Deseleziona tutti" : "Seleziona tutti"}
          />
        );
      },
      renderCell: (row) => (
        <Checkbox
          checked={selectedCavi.includes(row.id_cavo)}
          onChange={() => handleCavoToggle(row.id_cavo)}
          size="small"
          onClick={(e) => e.stopPropagation()}
        />
      )
    }] : []),
    {
      field: 'id_cavo',
      headerName: 'ID Cavo',
      dataType: 'text',
      headerStyle: { fontWeight: 'bold' }
    },
    // Colonna Revisione rimossa e spostata nella tabella delle statistiche
    {
      field: 'sistema',
      headerName: 'Sistema',
      dataType: 'text'
    },
    {
      field: 'utility',
      headerName: 'Utility',
      dataType: 'text'
    },
    {
      field: 'tipologia',
      headerName: 'Tipologia',
      dataType: 'text'
    },
    // n_conduttori field is now a spare field (kept in DB but hidden in UI)
    {
      field: 'sezione',
      headerName: 'Formazione',
      dataType: 'text',
      align: 'right',
      cellStyle: { textAlign: 'right' }
    },
    {
      field: 'metri_teorici',
      headerName: 'Metri Teorici',
      dataType: 'number',
      align: 'right',
      cellStyle: { textAlign: 'right' },
      renderCell: (row) => row.metri_teorici ? row.metri_teorici.toFixed(1) : '0'
    },
    {
      field: 'metratura_reale',
      headerName: 'Metri Reali',
      dataType: 'number',
      align: 'right',
      cellStyle: { textAlign: 'right' },
      renderCell: (row) => row.metratura_reale ? row.metratura_reale.toFixed(1) : '0'
    },
    {
      field: 'stato_installazione',
      headerName: 'Stato',
      dataType: 'text',
      renderCell: (row) => {
        // Verifica se il cavo è assegnato a una comanda
        const comandaPosa = row.comanda_posa;
        const comandaPartenza = row.comanda_partenza;
        const comandaArrivo = row.comanda_arrivo;
        const comandaCertificazione = row.comanda_certificazione;

        // Trova la comanda attiva (priorità: posa > partenza > arrivo > certificazione)
        const comandaAttiva = comandaPosa || comandaPartenza || comandaArrivo || comandaCertificazione;

        // Se c'è una comanda attiva e lo stato è "In corso", mostra il codice comanda
        if (comandaAttiva && row.stato_installazione === 'In corso') {
          return (
            <Chip
              label={comandaAttiva}
              size="small"
              color="primary"
              variant="filled"
              onClick={onStatusAction ? (e) => {
                e.stopPropagation();
                onStatusAction(row, 'view_command', `Visualizza comanda ${comandaAttiva}`, comandaAttiva);
              } : undefined}
              sx={{
                cursor: onStatusAction ? 'pointer' : 'default',
                backgroundColor: '#1976d2',
                color: 'white',
                fontWeight: 'bold',
                '&:hover': onStatusAction ? {
                  backgroundColor: '#1565c0 !important',
                  transform: 'scale(1.05)',
                  boxShadow: '0 2px 8px rgba(25, 118, 210, 0.3)'
                } : {},
                transition: 'all 0.2s ease',
                '&:active': {
                  transform: 'scale(0.98) !important'
                }
              }}
            />
          );
        }

        // Logica normale per gli altri stati
        const color = 'default';

        return (
          <Chip
            label={row.stato_installazione || 'N/D'}
            size="small"
            color={color}
            variant="outlined"
            onClick={onStatusAction ? (e) => {
              e.stopPropagation();
              if (row.stato_installazione === 'Installato') {
                onStatusAction(row, 'modify_reel', 'Modifica Bobina');
              } else if (row.stato_installazione === 'Da installare') {
                onStatusAction(row, 'insert_meters', 'Inserisci Metri Posati');
              }
            } : undefined}
            sx={{
              cursor: onStatusAction && (row.stato_installazione === 'Installato' || row.stato_installazione === 'Da installare') ? 'pointer' : 'default',
              backgroundColor: onStatusAction && (row.stato_installazione === 'Installato' || row.stato_installazione === 'Da installare') ? 'rgba(33, 150, 243, 0.1)' : undefined,
              color: onStatusAction && (row.stato_installazione === 'Installato' || row.stato_installazione === 'Da installare') ? '#1976d2' : undefined,
              border: onStatusAction && (row.stato_installazione === 'Installato' || row.stato_installazione === 'Da installare') ? '1px solid rgba(33, 150, 243, 0.3)' : undefined,
              '&:hover': onStatusAction && (row.stato_installazione === 'Installato' || row.stato_installazione === 'Da installare') ? {
                backgroundColor: 'rgba(33, 150, 243, 0.2) !important',
                color: '#1565c0 !important'
              } : {},
              transform: 'none !important',
              transition: 'background-color 0.2s ease'
            }}
          />
        );
      }
    },
    {
      field: 'id_bobina',
      headerName: 'Bobina',
      dataType: 'text',
      renderCell: (row) => {
        // Gestione differenziata per null e BOBINA_VUOTA
        if (row.id_bobina === null) {
          // Per cavi non posati (id_bobina è null)
          return '-';
        } else if (row.id_bobina === 'BOBINA_VUOTA') {
          // Per cavi posati senza bobina specifica
          return 'BOBINA VUOTA';
        } else if (!row.id_bobina) {
          // Per altri casi in cui id_bobina è falsy (undefined, stringa vuota)
          return '-';
        }

        // Estrai solo il numero della bobina (parte dopo '_B')
        const match = row.id_bobina.match(/_B(.+)$/);
        return match ? match[1] : row.id_bobina;
      }
    },
    {
      field: 'collegamenti',
      headerName: 'Collegamenti',
      dataType: 'string',
      align: 'center',
      cellStyle: { textAlign: 'center' },
      renderCell: (row) => {
        // Verifica se il cavo è installato
        const isInstallato = row.stato_installazione === 'Installato';

        if (!isInstallato) {
          // Cavo non installato - mostra simbolo disattivato
          return (
            <Chip
              label="✕ Non disponibile"
              size="small"
              variant="outlined"
              disabled
              sx={{
                cursor: 'not-allowed',
                backgroundColor: '#f5f5f5',
                color: '#9e9e9e',
                transform: 'none !important',
                transition: 'none !important'
              }}
            />
          );
        }

        // Cavo installato - logica normale
        const collegamenti = row.collegamenti || 0;
        let statoTestuale = '';
        let actionType = '';
        let chipColor = 'default';

        if (collegamenti === 0) {
          statoTestuale = '⚪⚪ Collega cavo';
          actionType = 'connect_cable';
          chipColor = 'default';
        } else if (collegamenti === 1) {
          statoTestuale = '🟢⚪ Completa collegamento';
          actionType = 'connect_arrival';
          chipColor = 'warning';
        } else if (collegamenti === 2) {
          statoTestuale = '⚪🟢 Completa collegamento';
          actionType = 'connect_departure';
          chipColor = 'warning';
        } else if (collegamenti === 3) {
          statoTestuale = '🟢🟢 Scollega cavo';
          actionType = 'disconnect_cable';
          chipColor = 'success';
        } else {
          statoTestuale = `Gestisci collegamenti`;
          actionType = 'manage_connections';
          chipColor = 'default';
        }

        return (
          <Chip
            label={statoTestuale}
            size="small"
            color={chipColor}
            variant="outlined"
            onClick={onStatusAction ? (e) => {
              e.stopPropagation();
              onStatusAction(row, actionType, statoTestuale);
            } : undefined}
            sx={{
              cursor: onStatusAction ? 'pointer' : 'default',
              backgroundColor: onStatusAction ? 'rgba(33, 150, 243, 0.1)' : undefined, // Azzurro molto chiaro
              color: onStatusAction ? '#1976d2' : undefined, // Blu scuro per il testo
              border: onStatusAction ? '1px solid rgba(33, 150, 243, 0.3)' : undefined,
              '&:hover': onStatusAction ? {
                backgroundColor: 'rgba(33, 150, 243, 0.2) !important', // Azzurro più intenso al hover
                color: '#1565c0 !important' // Blu ancora più scuro
              } : {},
              transform: 'none !important',
              transition: 'background-color 0.2s ease',
              '&:active': {
                transform: 'none !important'
              },
              '&:focus': {
                transform: 'none !important'
              }
            }}
          />
        );
      }
    },
    {
      field: 'certificato',
      headerName: 'Certificato',
      dataType: 'string',
      align: 'center',
      cellStyle: { textAlign: 'center' },
      renderCell: (row) => {
        const isCertificato = row.certificato === true || row.certificato === 'SI' || row.certificato === 'CERTIFICATO';
        const isInstallato = row.stato_installazione === 'Installato';

        // Verifica se il cavo può essere certificato (deve essere solo installato)
        // Il collegamento non è vincolante perché il sistema può collegarlo automaticamente
        const canBeCertified = isInstallato;

        let label, actionType, chipColor, isClickable;

        if (isCertificato) {
          label = 'Genera PDF';
          actionType = 'generate_pdf';
          chipColor = 'success';
          isClickable = true;
        } else if (!canBeCertified) {
          // Cavo non può essere certificato - solo se non è installato
          label = '✕ Non disponibile';
          actionType = null;
          chipColor = 'default';
          isClickable = false;
        } else {
          // Cavo può essere certificato (è installato)
          label = 'Certifica cavo';
          actionType = 'create_certificate';
          chipColor = 'primary';
          isClickable = true;
        }

        return (
          <Chip
            label={label}
            size="small"
            color={chipColor}
            variant={isCertificato ? 'filled' : 'outlined'}
            disabled={!isClickable}
            onClick={isClickable && onStatusAction ? (e) => {
              e.stopPropagation();
              onStatusAction(row, actionType, label);
            } : undefined}
            sx={{
              fontSize: '0.75rem',
              fontWeight: 500,
              minWidth: '90px',
              cursor: isClickable && onStatusAction ? 'pointer' : 'default',
              backgroundColor: isClickable && onStatusAction ? 'rgba(33, 150, 243, 0.1)' : undefined,
              color: isClickable && onStatusAction ? '#1976d2' : undefined,
              border: isClickable && onStatusAction ? '1px solid rgba(33, 150, 243, 0.3)' : undefined,
              '&:hover': isClickable && onStatusAction ? {
                backgroundColor: 'rgba(33, 150, 243, 0.2) !important',
                color: '#1565c0 !important'
              } : {},
              transform: 'none !important',
              transition: 'background-color 0.2s ease',
              '&:active': {
                transform: 'none !important'
              },
              '&:focus': {
                transform: 'none !important'
              },
              // Stile per stato disabilitato
              '&.Mui-disabled': !isClickable ? {
                backgroundColor: '#f5f5f5',
                color: '#9e9e9e',
                cursor: 'not-allowed'
              } : {}
            }}
          />
        );
      }
    }
  ];

  // Renderizza una riga personalizzata
  const renderRow = (row, index) => {
    // Se la selezione è abilitata, evidenzia le righe selezionate
    const isSelected = selectionEnabled && selectedCavi.includes(row.id_cavo);
    const selectedColor = 'rgba(25, 118, 210, 0.2)';
    const restColor = '#f5f7fa'; // Grigio-azzurro chiarissimo
    const hoverColor = 'rgba(33, 150, 243, 0.1)'; // Blu tecnico Material Blue 500 con opacità 10%

    return (
      <TableRow
        key={index}
        selected={isSelected}
        hover
        onClick={selectionEnabled ? () => handleCavoToggle(row.id_cavo) : undefined}
        onContextMenu={(e) => contextMenuItems.length > 0 ? handleContextMenu(e, row) : undefined}
        sx={{
          backgroundColor: `${isSelected ? selectedColor : restColor} !important`,
          cursor: selectionEnabled ? 'pointer' : 'default',
          '&:hover': {
            backgroundColor: `${selectionEnabled ? selectedColor : hoverColor} !important`
          }
        }}
      >
        {columns.map((column) => (
          <TableCell
            key={column.field}
            align={column.align || 'left'}
            sx={{
              ...column.cellStyle,
              backgroundColor: 'inherit !important' // Eredita il colore dalla riga
            }}
          >
            {column.renderCell ? column.renderCell(row) : row[column.field]}
          </TableCell>
        ))}
      </TableRow>
    );
  };



  return (
    <Box>
      {/* Filtro intelligente */}
      <SmartCaviFilter
        cavi={cavi}
        onFilteredDataChange={handleSmartFilterChange}
        loading={loading}
        selectionEnabled={selectionEnabled}
        onSelectionToggle={onSelectionToggle}
      />



      {/* Tabella con filtri Excel-like sui dati già filtrati dal filtro intelligente */}
      <FilterableTable
        data={smartFilteredCavi}
        columns={columns}
        onFilteredDataChange={handleFilteredDataChange}
        loading={loading}
        emptyMessage="Nessun cavo disponibile"
        renderRow={renderRow}
      />

      {/* Menu contestuale */}
      <ContextMenu
        open={contextMenu.open}
        anchorPosition={contextMenu.anchorPosition}
        onClose={closeContextMenu}
        menuItems={typeof contextMenuItems === 'function' ? contextMenuItems(contextMenu.contextData) : contextMenuItems}
        contextData={contextMenu.contextData}
      />
    </Box>
  );
};

export default CaviFilterableTable;
