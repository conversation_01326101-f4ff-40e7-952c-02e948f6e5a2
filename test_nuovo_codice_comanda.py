#!/usr/bin/env python3
"""
Test della nuova logica di generazione codici comanda semplificati
"""

import sys
import os
sys.path.append('.')
sys.path.append('./modules')

def test_generazione_codici():
    """Test della nuova generazione codici comanda"""
    print("🧪 === TEST NUOVA GENERAZIONE CODICI COMANDA ===")
    
    try:
        from modules.comande_new import genera_codice_comanda
        
        # Test per diversi tipi di comanda
        tipi_comanda = ['POSA', 'COLLEGAMENTO_PARTENZA', 'COLLEGAMENTO_ARRIVO', 'CERTIFICAZIONE']
        id_cantiere = 1
        
        print(f"📋 Test con cantiere {id_cantiere}")
        print("\n🔍 Generazione codici per ogni tipo:")
        
        codici_generati = []
        
        for tipo in tipi_comanda:
            try:
                codice = genera_codice_comanda(id_cantiere, tipo)
                codici_generati.append(codice)
                print(f"   {tipo:20} → {codice}")
            except Exception as e:
                print(f"   {tipo:20} → ❌ Errore: {e}")
        
        print(f"\n📊 Risultati:")
        print(f"   - Codici generati: {len(codici_generati)}")
        print(f"   - Codici univoci: {len(set(codici_generati))}")
        print(f"   - Formato corretto: {all(len(c) <= 6 for c in codici_generati)}")
        
        # Test generazione multipla dello stesso tipo
        print(f"\n🔄 Test generazione multipla POSA:")
        codici_posa = []
        for i in range(3):
            codice = genera_codice_comanda(id_cantiere, 'POSA')
            codici_posa.append(codice)
            print(f"   Tentativo {i+1}: {codice}")
        
        print(f"\n📊 Risultati POSA:")
        print(f"   - Codici generati: {len(codici_posa)}")
        print(f"   - Codici univoci: {len(set(codici_posa))}")
        print(f"   - Progressione corretta: {codici_posa == sorted(codici_posa)}")
        
        # Verifica formato
        print(f"\n✅ Verifica formato:")
        for codice in codici_generati + codici_posa:
            lunghezza = len(codice)
            prefisso = codice[:3]
            numero = codice[3:]
            
            formato_ok = (
                lunghezza <= 6 and
                prefisso.isalpha() and
                numero.isdigit() and
                len(numero) == 3
            )
            
            print(f"   {codice}: lunghezza={lunghezza}, prefisso={prefisso}, numero={numero} → {'✅' if formato_ok else '❌'}")
        
        return True
        
    except Exception as e:
        print(f"❌ Errore durante il test: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_confronto_vecchio_nuovo():
    """Confronto tra vecchio e nuovo formato"""
    print("\n🔄 === CONFRONTO FORMATI ===")
    
    print("📋 Formato VECCHIO (complesso):")
    print("   POS_1_20250610065219188")
    print("   CPT_1_20250610065219234")
    print("   CAR_1_20250610065219567")
    print("   CER_1_20250610065219789")
    
    print("\n📋 Formato NUOVO (semplice):")
    print("   POS001")
    print("   CPT002") 
    print("   CAR003")
    print("   CER004")
    
    print("\n✅ Vantaggi del nuovo formato:")
    print("   - 🎯 Lunghezza ridotta: 6 caratteri vs 21 caratteri")
    print("   - 📖 Più leggibile e memorizzabile")
    print("   - 🔢 Numerazione progressiva intuitiva")
    print("   - 💾 Meno spazio nel database")
    print("   - 🖥️ Migliore visualizzazione nell'interfaccia")
    print("   - 📱 Più adatto per app mobile")
    print("   - 🗣️ Più facile da comunicare verbalmente")

def main():
    print("🚀 AVVIO TEST NUOVI CODICI COMANDA")
    print("="*50)
    
    # Test 1: Generazione codici
    test1_ok = test_generazione_codici()
    
    # Test 2: Confronto formati
    test_confronto_vecchio_nuovo()
    
    # Risultati
    print("\n" + "="*50)
    print("📋 RIEPILOGO TEST")
    print("="*50)
    print(f"✅ Generazione codici: {'PASS' if test1_ok else 'FAIL'}")
    
    if test1_ok:
        print("\n🎉 NUOVO SISTEMA CODICI IMPLEMENTATO!")
        print("\n📝 PROSSIMI PASSI:")
        print("1. Testa la creazione di nuove comande")
        print("2. Verifica che i codici appaiano correttamente nell'interfaccia")
        print("3. Controlla che il click sui codici funzioni")
        print("4. I codici esistenti continueranno a funzionare normalmente")
    else:
        print("\n❌ PROBLEMI RILEVATI!")
        print("Verificare i problemi segnalati sopra.")
    
    return test1_ok

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
