{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\components\\\\comande\\\\ComandeListRivoluzionato.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useSearchParams } from 'react-router-dom';\nimport { Box, Typography, Button, Paper, Chip, IconButton, Dialog, DialogTitle, DialogContent, DialogActions, TextField, Alert, CircularProgress, Tooltip, List, ListItem, ListItemText, Accordion, AccordionSummary, AccordionDetails, Stack, MenuItem, Divider } from '@mui/material';\nimport { Add as AddIcon, Edit as EditIcon, Delete as DeleteIcon, Assignment as AssignIcon, Person as PersonIcon, Email as EmailIcon, Phone as PhoneIcon, ExpandMore as ExpandMoreIcon, CheckCircle as CheckCircleIcon, Verified as VerifiedIcon, Visibility as ViewIcon } from '@mui/icons-material';\nimport comandeService from '../../services/comandeService';\nimport responsabiliService from '../../services/responsabiliService';\nimport CreaComandaConCavi from './CreaComandaConCavi';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ComandeListRivoluzionato = ({\n  cantiereId,\n  cantiereName\n}) => {\n  _s();\n  // Hook per gestire i parametri URL\n  const [searchParams, setSearchParams] = useSearchParams();\n\n  // Stati principali - Responsabili come elemento principale\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [searchingComanda, setSearchingComanda] = useState(null);\n\n  // Stati comande\n  const [statistiche, setStatistiche] = useState(null);\n  const [openCreaConCavi, setOpenCreaConCavi] = useState(false);\n\n  // Stati responsabili\n  const [responsabili, setResponsabili] = useState([]);\n  const [loadingResponsabili, setLoadingResponsabili] = useState(false);\n  const [comandePerResponsabile, setComandePerResponsabile] = useState({});\n  const [openResponsabileDialog, setOpenResponsabileDialog] = useState(false);\n  const [dialogModeResponsabile, setDialogModeResponsabile] = useState('create');\n  const [selectedResponsabile, setSelectedResponsabile] = useState(null);\n  const [formDataResponsabile, setFormDataResponsabile] = useState({\n    nome_responsabile: '',\n    email: '',\n    telefono: ''\n  });\n\n  // Stati per dialog comande\n  const [openComandaDialog, setOpenComandaDialog] = useState(false);\n  const [dialogModeComanda, setDialogModeComanda] = useState('view'); // 'view', 'edit'\n  const [selectedComanda, setSelectedComanda] = useState(null);\n  const [formDataComanda, setFormDataComanda] = useState({\n    tipo_comanda: 'POSA',\n    descrizione: '',\n    responsabile: '',\n    data_scadenza: '',\n    priorita: 'NORMALE',\n    note_capo_cantiere: ''\n  });\n  const loadComande = async () => {\n    try {\n      setLoading(true);\n      // Non serve più salvare le comande in uno stato separato\n      // Le comande vengono caricate per responsabile\n      setError(null);\n    } catch (err) {\n      console.error('Errore nel caricamento delle comande:', err);\n      setError('Errore nel caricamento delle comande');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const loadStatistiche = async () => {\n    try {\n      const stats = await comandeService.getStatisticheComande(cantiereId);\n      setStatistiche(stats);\n    } catch (err) {\n      console.error('Errore nel caricamento delle statistiche:', err);\n    }\n  };\n  const loadResponsabili = async () => {\n    try {\n      setLoadingResponsabili(true);\n      setError(null);\n      const data = await responsabiliService.getResponsabiliCantiere(cantiereId);\n      setResponsabili(data || []);\n      await loadComandePerResponsabili(data || []);\n    } catch (err) {\n      var _err$response, _err$response$data;\n      console.error('Errore nel caricamento dei responsabili:', err);\n      const errorMessage = ((_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.detail) || err.message || 'Errore nel caricamento dei responsabili';\n      setError(`Errore nel caricamento dei responsabili: ${errorMessage}`);\n    } finally {\n      setLoadingResponsabili(false);\n    }\n  };\n\n  // Carica dati al mount - Focus sui responsabili\n  useEffect(() => {\n    if (cantiereId) {\n      loadResponsabili();\n      loadComande();\n      loadStatistiche();\n    }\n  }, [cantiereId]);\n\n  // Gestione parametro URL per aprire comanda specifica\n  useEffect(() => {\n    const comandaParam = searchParams.get('comanda');\n    console.log('🔍 Controllo parametro URL comanda:', comandaParam);\n    console.log('📊 Stato dati:', {\n      responsabili: responsabili.length,\n      comandePerResponsabile: Object.keys(comandePerResponsabile).length,\n      loading,\n      loadingResponsabili\n    });\n\n    // Imposta lo stato di ricerca\n    if (comandaParam && comandaParam !== searchingComanda) {\n      setSearchingComanda(comandaParam);\n    }\n    if (comandaParam && responsabili.length > 0 && Object.keys(comandePerResponsabile).length > 0) {\n      console.log('🔎 Ricerca comanda tra i responsabili...');\n\n      // Cerca la comanda tra tutti i responsabili\n      let comandaTrovata = null;\n      for (const responsabile of responsabili) {\n        const comandeResp = comandePerResponsabile[responsabile.id_responsabile] || [];\n        console.log(`📋 Responsabile ${responsabile.nome_responsabile}: ${comandeResp.length} comande`);\n        comandeTrovata = comandeResp.find(c => c.codice_comanda === comandaParam);\n        if (comandaTrovata) {\n          console.log('✅ Comanda trovata:', comandaTrovata);\n          break;\n        }\n      }\n      if (comandaTrovata) {\n        console.log('🎯 Apertura automatica comanda da URL:', comandaParam);\n        setSearchingComanda(null); // Rimuovi lo stato di ricerca\n        handleOpenComandaDialog('view', comandaTrovata);\n        // Rimuovi il parametro dall'URL per evitare riaperture\n        setTimeout(() => {\n          setSearchParams(prev => {\n            const newParams = new URLSearchParams(prev);\n            newParams.delete('comanda');\n            return newParams;\n          });\n        }, 100);\n      } else {\n        console.warn('⚠️ Comanda non trovata:', comandaParam);\n        console.log('📋 Comande disponibili:');\n        responsabili.forEach(resp => {\n          const comande = comandePerResponsabile[resp.id_responsabile] || [];\n          comande.forEach(cmd => {\n            console.log(`  - ${cmd.codice_comanda} (${resp.nome_responsabile})`);\n          });\n        });\n\n        // Se non troviamo la comanda, potrebbe essere che i dati non sono completi\n        // Riprova dopo un breve delay\n        if (!loading && !loadingResponsabili) {\n          console.log('🔄 Tentativo di ricaricamento dati...');\n          setTimeout(() => {\n            loadResponsabili();\n          }, 500);\n        }\n      }\n    } else if (comandaParam) {\n      console.log('⏳ Dati non ancora caricati, attendo...');\n\n      // Se abbiamo un parametro ma i dati non sono caricati, forza il caricamento\n      if (!loading && !loadingResponsabili && responsabili.length === 0) {\n        console.log('🚀 Forzatura caricamento responsabili...');\n        loadResponsabili();\n      }\n    }\n  }, [searchParams, responsabili, comandePerResponsabile, loading, loadingResponsabili]);\n  const loadComandePerResponsabili = async responsabiliList => {\n    try {\n      const comandeMap = {};\n      for (const responsabile of responsabiliList) {\n        try {\n          const response = await comandeService.getComandeByResponsabile(cantiereId, responsabile.nome_responsabile);\n          // Assicurati che sia sempre un array\n          let comande = [];\n          if (response && Array.isArray(response)) {\n            comande = response;\n          } else if (response && response.comande && Array.isArray(response.comande)) {\n            comande = response.comande;\n          } else if (response && response.data && Array.isArray(response.data)) {\n            comande = response.data;\n          }\n          comandeMap[responsabile.id_responsabile] = comande;\n        } catch (err) {\n          console.error(`Errore nel caricamento comande per ${responsabile.nome_responsabile}:`, err);\n          comandeMap[responsabile.id_responsabile] = [];\n        }\n      }\n      setComandePerResponsabile(comandeMap);\n    } catch (err) {\n      console.error('Errore nel caricamento delle comande:', err);\n    }\n  };\n\n  // Gestione responsabili\n  const handleOpenResponsabileDialog = (mode, responsabile = null) => {\n    setDialogModeResponsabile(mode);\n    setSelectedResponsabile(responsabile);\n    if (mode === 'edit' && responsabile) {\n      setFormDataResponsabile({\n        nome_responsabile: responsabile.nome_responsabile || '',\n        email: responsabile.email || '',\n        telefono: responsabile.telefono || ''\n      });\n    } else {\n      setFormDataResponsabile({\n        nome_responsabile: '',\n        email: '',\n        telefono: ''\n      });\n    }\n    setOpenResponsabileDialog(true);\n  };\n  const handleCloseResponsabileDialog = () => {\n    setOpenResponsabileDialog(false);\n    setSelectedResponsabile(null);\n    setError(null);\n  };\n  const handleSubmitResponsabile = async () => {\n    try {\n      setError(null);\n      if (!formDataResponsabile.nome_responsabile.trim()) {\n        setError('Il nome del responsabile è obbligatorio');\n        return;\n      }\n      if (!formDataResponsabile.email && !formDataResponsabile.telefono) {\n        setError('Almeno uno tra email e telefono deve essere specificato');\n        return;\n      }\n      if (dialogModeResponsabile === 'create') {\n        await responsabiliService.createResponsabile(cantiereId, formDataResponsabile);\n      } else if (dialogModeResponsabile === 'edit') {\n        await responsabiliService.updateResponsabile(selectedResponsabile.id_responsabile, formDataResponsabile);\n      }\n      handleCloseResponsabileDialog();\n      await loadResponsabili();\n    } catch (err) {\n      console.error('Errore nel salvataggio:', err);\n      setError(err.detail || 'Errore nel salvataggio del responsabile');\n    }\n  };\n  const handleDeleteResponsabile = async idResponsabile => {\n    if (!window.confirm('Sei sicuro di voler eliminare questo responsabile?')) {\n      return;\n    }\n    try {\n      await responsabiliService.deleteResponsabile(idResponsabile);\n      await loadResponsabili();\n    } catch (err) {\n      console.error('Errore nell\\'eliminazione:', err);\n      setError('Errore nell\\'eliminazione del responsabile');\n    }\n  };\n\n  // Gestione comande\n  const handleOpenComandaDialog = (mode, comanda = null) => {\n    setDialogModeComanda(mode);\n    setSelectedComanda(comanda);\n    if (mode === 'edit' && comanda) {\n      setFormDataComanda({\n        tipo_comanda: comanda.tipo_comanda,\n        descrizione: comanda.descrizione || '',\n        responsabile: comanda.responsabile || '',\n        data_scadenza: comanda.data_scadenza || '',\n        priorita: comanda.priorita || 'NORMALE',\n        note_capo_cantiere: comanda.note_capo_cantiere || ''\n      });\n    }\n    setOpenComandaDialog(true);\n  };\n  const handleCloseComandaDialog = () => {\n    setOpenComandaDialog(false);\n    setSelectedComanda(null);\n    setFormDataComanda({\n      tipo_comanda: 'POSA',\n      descrizione: '',\n      responsabile: '',\n      data_scadenza: '',\n      priorita: 'NORMALE',\n      note_capo_cantiere: ''\n    });\n  };\n  const handleSubmitComanda = async () => {\n    try {\n      if (dialogModeComanda === 'edit') {\n        await comandeService.updateComanda(selectedComanda.codice_comanda, formDataComanda);\n        handleCloseComandaDialog();\n        await loadResponsabili(); // Ricarica per aggiornare le comande\n        await loadStatistiche();\n      }\n    } catch (err) {\n      console.error('Errore nel salvataggio:', err);\n      setError('Errore nel salvataggio della comanda');\n    }\n  };\n  const handleDeleteComanda = async codiceComanda => {\n    if (!window.confirm('Sei sicuro di voler eliminare questa comanda?')) {\n      return;\n    }\n    try {\n      await comandeService.deleteComanda(codiceComanda);\n      await loadResponsabili(); // Ricarica per aggiornare le comande\n      await loadStatistiche();\n    } catch (err) {\n      console.error('Errore nell\\'eliminazione:', err);\n      setError('Errore nell\\'eliminazione della comanda');\n    }\n  };\n  const getTipoComandaLabel = tipo => {\n    const labels = {\n      'POSA': 'Posa',\n      'COLLEGAMENTO_PARTENZA': 'Coll. Partenza',\n      'COLLEGAMENTO_ARRIVO': 'Coll. Arrivo',\n      'CERTIFICAZIONE': 'Certificazione',\n      'TESTING': 'Testing/Certificazione'\n    };\n    return labels[tipo] || tipo;\n  };\n  const getStatoColor = stato => {\n    const colors = {\n      'CREATA': 'default',\n      'ASSEGNATA': 'primary',\n      'IN_CORSO': 'warning',\n      'COMPLETATA': 'success',\n      'ANNULLATA': 'error'\n    };\n    return colors[stato] || 'default';\n  };\n  const getPrioritaColor = priorita => {\n    const colors = {\n      'BASSA': 'default',\n      'NORMALE': 'primary',\n      'ALTA': 'warning',\n      'URGENTE': 'error'\n    };\n    return colors[priorita] || 'default';\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      justifyContent: \"center\",\n      alignItems: \"center\",\n      minHeight: \"400px\",\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 400,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 399,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      p: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      mb: 3,\n      children: /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        sx: {\n          fontWeight: 600,\n          color: 'primary.main'\n        },\n        children: cantiereName\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 409,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 408,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"error\",\n      sx: {\n        mb: 2\n      },\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 415,\n      columnNumber: 9\n    }, this), searchingComanda && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"info\",\n      sx: {\n        mb: 2\n      },\n      children: [\"\\uD83D\\uDD0D Ricerca comanda \", searchingComanda, \" in corso...\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 421,\n      columnNumber: 9\n    }, this), statistiche && /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 2,\n        mb: 3,\n        bgcolor: 'grey.50'\n      },\n      children: /*#__PURE__*/_jsxDEV(Stack, {\n        direction: \"row\",\n        spacing: 4,\n        alignItems: \"center\",\n        justifyContent: \"space-between\",\n        flexWrap: \"wrap\",\n        children: [/*#__PURE__*/_jsxDEV(Stack, {\n          direction: \"row\",\n          alignItems: \"center\",\n          spacing: 1,\n          children: [/*#__PURE__*/_jsxDEV(PersonIcon, {\n            color: \"primary\",\n            fontSize: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 432,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              fontWeight: \"bold\",\n              sx: {\n                lineHeight: 1\n              },\n              children: statistiche.responsabili_attivi || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 434,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"text.secondary\",\n              children: \"Responsabili\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 437,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 433,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 431,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Stack, {\n          direction: \"row\",\n          alignItems: \"center\",\n          spacing: 1,\n          children: [/*#__PURE__*/_jsxDEV(AssignIcon, {\n            color: \"info\",\n            fontSize: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 445,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              fontWeight: \"bold\",\n              sx: {\n                lineHeight: 1\n              },\n              children: statistiche.totale_comande || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 447,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"text.secondary\",\n              children: \"Totale\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 450,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 446,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 444,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Stack, {\n          direction: \"row\",\n          alignItems: \"center\",\n          spacing: 1,\n          children: [/*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n            color: \"warning\",\n            fontSize: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 458,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              fontWeight: \"bold\",\n              sx: {\n                lineHeight: 1\n              },\n              children: statistiche.comande_in_corso || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 460,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"text.secondary\",\n              children: \"In Corso\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 463,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 459,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 457,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Stack, {\n          direction: \"row\",\n          alignItems: \"center\",\n          spacing: 1,\n          children: [/*#__PURE__*/_jsxDEV(VerifiedIcon, {\n            color: \"success\",\n            fontSize: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 471,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              fontWeight: \"bold\",\n              sx: {\n                lineHeight: 1\n              },\n              children: statistiche.comande_completate || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 473,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"text.secondary\",\n              children: \"Completate\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 476,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 472,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 470,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Stack, {\n          direction: \"row\",\n          alignItems: \"center\",\n          spacing: 1,\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              width: 32,\n              height: 32,\n              borderRadius: '50%',\n              bgcolor: statistiche.comande_completate / (statistiche.totale_comande || 1) >= 0.8 ? 'success.main' : statistiche.comande_completate / (statistiche.totale_comande || 1) >= 0.5 ? 'warning.main' : 'error.main',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center'\n            },\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              fontWeight: \"bold\",\n              color: \"white\",\n              children: [statistiche.totale_comande > 0 ? Math.round(statistiche.comande_completate / statistiche.totale_comande * 100) : 0, \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 494,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 484,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              fontWeight: \"medium\",\n              sx: {\n                lineHeight: 1\n              },\n              children: \"Completamento\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 499,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"text.secondary\",\n              children: [statistiche.comande_create || 0, \" create\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 502,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 498,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 483,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 429,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 428,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          justifyContent: \"space-between\",\n          alignItems: \"center\",\n          mb: 3,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h5\",\n            sx: {\n              fontWeight: 500,\n              color: 'text.primary'\n            },\n            children: \"Responsabili del Cantiere\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 516,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 521,\n              columnNumber: 26\n            }, this),\n            onClick: () => handleOpenResponsabileDialog('create'),\n            sx: {\n              textTransform: 'none',\n              fontWeight: 500,\n              px: 3,\n              py: 1,\n              backgroundColor: '#f8f9fa',\n              color: '#212529',\n              border: '1px solid #dee2e6',\n              boxShadow: 'none',\n              '&:hover': {\n                backgroundColor: '#e9ecef',\n                boxShadow: 'none'\n              }\n            },\n            children: \"Inserisci Responsabile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 519,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 515,\n          columnNumber: 11\n        }, this), loadingResponsabili ? /*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          justifyContent: \"center\",\n          py: 4,\n          children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 544,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 543,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(Box, {\n          children: responsabili.length === 0 ? /*#__PURE__*/_jsxDEV(Paper, {\n            elevation: 0,\n            sx: {\n              p: 6,\n              textAlign: 'center',\n              backgroundColor: 'grey.50',\n              border: '1px dashed',\n              borderColor: 'grey.300'\n            },\n            children: [/*#__PURE__*/_jsxDEV(PersonIcon, {\n              sx: {\n                fontSize: 48,\n                color: 'grey.400',\n                mb: 2\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 559,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              color: \"text.secondary\",\n              gutterBottom: true,\n              children: \"Nessun responsabile configurato\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 560,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              sx: {\n                mb: 3\n              },\n              children: \"Aggiungi il primo responsabile per iniziare a gestire le comande\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 563,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 568,\n                columnNumber: 32\n              }, this),\n              onClick: () => handleOpenResponsabileDialog('create'),\n              sx: {\n                textTransform: 'none',\n                backgroundColor: '#f8f9fa',\n                color: '#212529',\n                border: '1px solid #dee2e6',\n                boxShadow: 'none',\n                '&:hover': {\n                  backgroundColor: '#e9ecef',\n                  boxShadow: 'none'\n                }\n              },\n              children: \"Inserisci Primo Responsabile\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 566,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 549,\n            columnNumber: 17\n          }, this) : responsabili.map(responsabile => /*#__PURE__*/_jsxDEV(Accordion, {\n            sx: {\n              mb: 2,\n              '&:before': {\n                display: 'none'\n              },\n              boxShadow: '0 1px 3px rgba(0,0,0,0.1)',\n              border: '1px solid',\n              borderColor: 'grey.200'\n            },\n            children: [/*#__PURE__*/_jsxDEV(AccordionSummary, {\n              expandIcon: /*#__PURE__*/_jsxDEV(ExpandMoreIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 598,\n                columnNumber: 35\n              }, this),\n              sx: {\n                '&:hover': {\n                  backgroundColor: 'grey.50'\n                }\n              },\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                display: \"flex\",\n                alignItems: \"center\",\n                justifyContent: \"space-between\",\n                width: \"100%\",\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  display: \"flex\",\n                  alignItems: \"center\",\n                  gap: 2,\n                  children: [/*#__PURE__*/_jsxDEV(PersonIcon, {\n                    color: \"primary\",\n                    sx: {\n                      fontSize: 28\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 607,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"h6\",\n                      sx: {\n                        fontWeight: 500\n                      },\n                      children: responsabile.nome_responsabile\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 609,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(Box, {\n                      display: \"flex\",\n                      gap: 3,\n                      mt: 0.5,\n                      children: [responsabile.email && /*#__PURE__*/_jsxDEV(Box, {\n                        display: \"flex\",\n                        alignItems: \"center\",\n                        gap: 0.5,\n                        children: [/*#__PURE__*/_jsxDEV(EmailIcon, {\n                          fontSize: \"small\",\n                          color: \"action\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 615,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"body2\",\n                          color: \"text.secondary\",\n                          children: responsabile.email\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 616,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 614,\n                        columnNumber: 33\n                      }, this), responsabile.telefono && /*#__PURE__*/_jsxDEV(Box, {\n                        display: \"flex\",\n                        alignItems: \"center\",\n                        gap: 0.5,\n                        children: [/*#__PURE__*/_jsxDEV(PhoneIcon, {\n                          fontSize: \"small\",\n                          color: \"action\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 623,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"body2\",\n                          color: \"text.secondary\",\n                          children: responsabile.telefono\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 624,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 622,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 612,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 608,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 606,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  display: \"flex\",\n                  alignItems: \"center\",\n                  gap: 1,\n                  onClick: e => e.stopPropagation(),\n                  children: [/*#__PURE__*/_jsxDEV(Chip, {\n                    icon: /*#__PURE__*/_jsxDEV(AssignIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 635,\n                      columnNumber: 35\n                    }, this),\n                    label: `${Array.isArray(comandePerResponsabile[responsabile.id_responsabile]) ? comandePerResponsabile[responsabile.id_responsabile].length : 0} comande`,\n                    size: \"small\",\n                    color: \"primary\",\n                    variant: \"outlined\",\n                    clickable: true,\n                    onClick: () => {\n                      // Pre-seleziona il responsabile nel dialog di creazione comanda\n                      setOpenCreaConCavi(true);\n                    },\n                    sx: {\n                      fontWeight: 500,\n                      backgroundColor: 'rgba(33, 150, 243, 0.1)',\n                      color: '#1976d2',\n                      border: '1px solid rgba(33, 150, 243, 0.3)',\n                      '&:hover': {\n                        backgroundColor: 'rgba(33, 150, 243, 0.2) !important',\n                        color: '#1565c0 !important'\n                      }\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 634,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                    title: \"Modifica responsabile\",\n                    children: /*#__PURE__*/_jsxDEV(IconButton, {\n                      size: \"small\",\n                      onClick: () => handleOpenResponsabileDialog('edit', responsabile),\n                      sx: {\n                        color: '#6c757d',\n                        '&:hover': {\n                          backgroundColor: '#e9ecef'\n                        }\n                      },\n                      children: /*#__PURE__*/_jsxDEV(EditIcon, {\n                        fontSize: \"small\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 667,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 657,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 656,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                    title: \"Elimina responsabile\",\n                    children: /*#__PURE__*/_jsxDEV(IconButton, {\n                      size: \"small\",\n                      onClick: () => handleDeleteResponsabile(responsabile.id_responsabile),\n                      sx: {\n                        color: '#6c757d',\n                        '&:hover': {\n                          backgroundColor: '#e9ecef'\n                        }\n                      },\n                      children: /*#__PURE__*/_jsxDEV(DeleteIcon, {\n                        fontSize: \"small\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 681,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 671,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 670,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 633,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 605,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 597,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(AccordionDetails, {\n              sx: {\n                pt: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle2\",\n                gutterBottom: true,\n                sx: {\n                  fontWeight: 500,\n                  mb: 2\n                },\n                children: \"Comande Assegnate\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 689,\n                columnNumber: 23\n              }, this), !Array.isArray(comandePerResponsabile[responsabile.id_responsabile]) || comandePerResponsabile[responsabile.id_responsabile].length === 0 ? /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  p: 3,\n                  textAlign: 'center',\n                  backgroundColor: 'grey.50',\n                  borderRadius: 1,\n                  border: '1px dashed',\n                  borderColor: 'grey.300'\n                },\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: \"Nessuna comanda assegnata a questo responsabile\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 704,\n                  columnNumber: 27\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 694,\n                columnNumber: 25\n              }, this) : /*#__PURE__*/_jsxDEV(List, {\n                dense: true,\n                children: Array.isArray(comandePerResponsabile[responsabile.id_responsabile]) && comandePerResponsabile[responsabile.id_responsabile].map(comanda => /*#__PURE__*/_jsxDEV(ListItem, {\n                  divider: true,\n                  sx: {\n                    '&:hover': {\n                      backgroundColor: 'rgba(33, 150, 243, 0.1)'\n                    }\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(ListItemText, {\n                    primary: /*#__PURE__*/_jsxDEV(Box, {\n                      display: \"flex\",\n                      alignItems: \"center\",\n                      gap: 1,\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        fontWeight: \"bold\",\n                        children: comanda.codice_comanda\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 723,\n                        columnNumber: 37\n                      }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                        label: getTipoComandaLabel(comanda.tipo_comanda),\n                        size: \"small\",\n                        variant: \"outlined\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 726,\n                        columnNumber: 37\n                      }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                        label: comanda.stato || 'CREATA',\n                        size: \"small\",\n                        color: getStatoColor(comanda.stato)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 731,\n                        columnNumber: 37\n                      }, this), comanda.priorita && comanda.priorita !== 'NORMALE' && /*#__PURE__*/_jsxDEV(Chip, {\n                        label: comanda.priorita,\n                        size: \"small\",\n                        color: getPrioritaColor(comanda.priorita),\n                        variant: \"filled\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 737,\n                        columnNumber: 39\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 722,\n                      columnNumber: 35\n                    }, this),\n                    secondary: /*#__PURE__*/_jsxDEV(Box, {\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        color: \"textSecondary\",\n                        children: [comanda.descrizione || 'Nessuna descrizione', comanda.data_creazione && ` • Creata: ${new Date(comanda.data_creazione).toLocaleDateString()}`]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 748,\n                        columnNumber: 37\n                      }, this), comanda.numero_cavi_assegnati > 0 && /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        color: \"primary\",\n                        children: [comanda.numero_cavi_assegnati, \" cavi assegnati\", comanda.percentuale_completamento && ` • ${comanda.percentuale_completamento.toFixed(1)}% completato`]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 753,\n                        columnNumber: 39\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 747,\n                      columnNumber: 35\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 720,\n                    columnNumber: 31\n                  }, this), /*#__PURE__*/_jsxDEV(Box, {\n                    display: \"flex\",\n                    gap: 0.5,\n                    ml: 1,\n                    children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n                      title: \"Visualizza\",\n                      children: /*#__PURE__*/_jsxDEV(IconButton, {\n                        size: \"small\",\n                        onClick: () => handleOpenComandaDialog('view', comanda),\n                        sx: {\n                          color: '#6c757d',\n                          '&:hover': {\n                            backgroundColor: '#e9ecef'\n                          }\n                        },\n                        children: /*#__PURE__*/_jsxDEV(ViewIcon, {\n                          fontSize: \"small\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 773,\n                          columnNumber: 37\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 763,\n                        columnNumber: 35\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 762,\n                      columnNumber: 33\n                    }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                      title: \"Modifica\",\n                      children: /*#__PURE__*/_jsxDEV(IconButton, {\n                        size: \"small\",\n                        onClick: () => handleOpenComandaDialog('edit', comanda),\n                        sx: {\n                          color: '#6c757d',\n                          '&:hover': {\n                            backgroundColor: '#e9ecef'\n                          }\n                        },\n                        children: /*#__PURE__*/_jsxDEV(EditIcon, {\n                          fontSize: \"small\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 787,\n                          columnNumber: 37\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 777,\n                        columnNumber: 35\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 776,\n                      columnNumber: 33\n                    }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                      title: \"Elimina\",\n                      children: /*#__PURE__*/_jsxDEV(IconButton, {\n                        size: \"small\",\n                        onClick: () => handleDeleteComanda(comanda.codice_comanda),\n                        sx: {\n                          color: '#6c757d',\n                          '&:hover': {\n                            backgroundColor: '#e9ecef'\n                          }\n                        },\n                        children: /*#__PURE__*/_jsxDEV(DeleteIcon, {\n                          fontSize: \"small\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 801,\n                          columnNumber: 37\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 791,\n                        columnNumber: 35\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 790,\n                      columnNumber: 33\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 761,\n                    columnNumber: 31\n                  }, this)]\n                }, comanda.codice_comanda, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 711,\n                  columnNumber: 29\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 709,\n                columnNumber: 25\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 688,\n              columnNumber: 21\n            }, this)]\n          }, responsabile.id_responsabile, true, {\n            fileName: _jsxFileName,\n            lineNumber: 587,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 547,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 513,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 512,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: openResponsabileDialog,\n      onClose: handleCloseResponsabileDialog,\n      maxWidth: \"sm\",\n      fullWidth: true,\n      PaperProps: {\n        sx: {\n          borderRadius: 2\n        }\n      },\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        sx: {\n          pb: 1\n        },\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            fontWeight: 600\n          },\n          children: dialogModeResponsabile === 'create' ? 'Inserisci Responsabile' : 'Modifica Responsabile'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 829,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 828,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            pt: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Nome Responsabile\",\n            value: formDataResponsabile.nome_responsabile,\n            onChange: e => setFormDataResponsabile({\n              ...formDataResponsabile,\n              nome_responsabile: e.target.value\n            }),\n            margin: \"normal\",\n            required: true,\n            variant: \"outlined\",\n            sx: {\n              mb: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 835,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Email\",\n            type: \"email\",\n            value: formDataResponsabile.email,\n            onChange: e => setFormDataResponsabile({\n              ...formDataResponsabile,\n              email: e.target.value\n            }),\n            margin: \"normal\",\n            variant: \"outlined\",\n            helperText: \"Email per notifiche (opzionale se inserisci telefono)\",\n            sx: {\n              mb: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 846,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Telefono\",\n            value: formDataResponsabile.telefono,\n            onChange: e => setFormDataResponsabile({\n              ...formDataResponsabile,\n              telefono: e.target.value\n            }),\n            margin: \"normal\",\n            variant: \"outlined\",\n            helperText: \"Numero per SMS (opzionale se inserisci email)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 858,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 834,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 833,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        sx: {\n          p: 3,\n          pt: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCloseResponsabileDialog,\n          sx: {\n            textTransform: 'none'\n          },\n          children: \"Annulla\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 870,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleSubmitResponsabile,\n          variant: \"contained\",\n          sx: {\n            textTransform: 'none',\n            fontWeight: 500,\n            px: 3\n          },\n          children: dialogModeResponsabile === 'create' ? 'Crea' : 'Salva'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 876,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 869,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 819,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: openComandaDialog,\n      onClose: handleCloseComandaDialog,\n      maxWidth: \"md\",\n      fullWidth: true,\n      PaperProps: {\n        sx: {\n          borderRadius: 2\n        }\n      },\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        sx: {\n          pb: 1\n        },\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            fontWeight: 600\n          },\n          children: dialogModeComanda === 'view' ? 'Dettagli Comanda' : 'Modifica Comanda'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 901,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 900,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            pt: 2\n          },\n          children: dialogModeComanda === 'view' && selectedComanda ? /*#__PURE__*/_jsxDEV(List, {\n            children: [/*#__PURE__*/_jsxDEV(ListItem, {\n              children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: \"Codice Comanda\",\n                secondary: selectedComanda.codice_comanda\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 910,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 909,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 915,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n              children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: \"Tipo\",\n                secondary: getTipoComandaLabel(selectedComanda.tipo_comanda)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 917,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 916,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 922,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n              children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: \"Stato\",\n                secondary: /*#__PURE__*/_jsxDEV(Chip, {\n                  label: selectedComanda.stato,\n                  color: getStatoColor(selectedComanda.stato),\n                  size: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 927,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 924,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 923,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 935,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n              children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: \"Descrizione\",\n                secondary: selectedComanda.descrizione || 'Nessuna descrizione'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 937,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 936,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 942,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n              children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: \"Priorit\\xE0\",\n                secondary: /*#__PURE__*/_jsxDEV(Chip, {\n                  label: selectedComanda.priorita || 'NORMALE',\n                  color: getPrioritaColor(selectedComanda.priorita || 'NORMALE'),\n                  size: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 947,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 944,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 943,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 955,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n              children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: \"Responsabile\",\n                secondary: selectedComanda.responsabile || 'Non assegnato'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 957,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 956,\n              columnNumber: 17\n            }, this), selectedComanda.note_capo_cantiere && /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 964,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Note Capo Cantiere\",\n                  secondary: selectedComanda.note_capo_cantiere\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 966,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 965,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 973,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n              children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: \"Data Creazione\",\n                secondary: new Date(selectedComanda.data_creazione).toLocaleDateString('it-IT')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 975,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 974,\n              columnNumber: 17\n            }, this), selectedComanda.data_scadenza && /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 982,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n                children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: \"Data Scadenza\",\n                  secondary: new Date(selectedComanda.data_scadenza).toLocaleDateString('it-IT')\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 984,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 983,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 991,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n              children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: \"Cavi Assegnati\",\n                secondary: selectedComanda.numero_cavi_assegnati || 0\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 993,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 992,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 998,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n              children: /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: \"Completamento\",\n                secondary: `${(selectedComanda.percentuale_completamento || 0).toFixed(1)}%`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1000,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 999,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 908,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              select: true,\n              label: \"Tipo Comanda\",\n              value: formDataComanda.tipo_comanda,\n              onChange: e => setFormDataComanda({\n                ...formDataComanda,\n                tipo_comanda: e.target.value\n              }),\n              margin: \"normal\",\n              sx: {\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"POSA\",\n                children: \"Posa\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1017,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"COLLEGAMENTO_PARTENZA\",\n                children: \"Collegamento Partenza\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1018,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"COLLEGAMENTO_ARRIVO\",\n                children: \"Collegamento Arrivo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1019,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"CERTIFICAZIONE\",\n                children: \"Certificazione\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1020,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"TESTING\",\n                children: \"Testing\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1021,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1008,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              select: true,\n              label: \"Priorit\\xE0\",\n              value: formDataComanda.priorita,\n              onChange: e => setFormDataComanda({\n                ...formDataComanda,\n                priorita: e.target.value\n              }),\n              margin: \"normal\",\n              sx: {\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"BASSA\",\n                children: \"Bassa\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1033,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"NORMALE\",\n                children: \"Normale\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1034,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"ALTA\",\n                children: \"Alta\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1035,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"URGENTE\",\n                children: \"Urgente\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1036,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1024,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Descrizione\",\n              value: formDataComanda.descrizione,\n              onChange: e => setFormDataComanda({\n                ...formDataComanda,\n                descrizione: e.target.value\n              }),\n              margin: \"normal\",\n              multiline: true,\n              rows: 3,\n              sx: {\n                mb: 2\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1039,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Responsabile\",\n              value: formDataComanda.responsabile,\n              onChange: e => setFormDataComanda({\n                ...formDataComanda,\n                responsabile: e.target.value\n              }),\n              margin: \"normal\",\n              required: true,\n              helperText: \"Chi eseguir\\xE0 il lavoro (obbligatorio)\",\n              sx: {\n                mb: 2\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1050,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Note Capo Cantiere\",\n              value: formDataComanda.note_capo_cantiere,\n              onChange: e => setFormDataComanda({\n                ...formDataComanda,\n                note_capo_cantiere: e.target.value\n              }),\n              margin: \"normal\",\n              multiline: true,\n              rows: 2,\n              helperText: \"Istruzioni specifiche per il responsabile\",\n              sx: {\n                mb: 2\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1061,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              fullWidth: true,\n              label: \"Data Scadenza\",\n              type: \"date\",\n              value: formDataComanda.data_scadenza,\n              onChange: e => setFormDataComanda({\n                ...formDataComanda,\n                data_scadenza: e.target.value\n              }),\n              margin: \"normal\",\n              InputLabelProps: {\n                shrink: true\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1073,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 906,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 905,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        sx: {\n          p: 3,\n          pt: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCloseComandaDialog,\n          sx: {\n            textTransform: 'none'\n          },\n          children: dialogModeComanda === 'view' ? 'Chiudi' : 'Annulla'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1089,\n          columnNumber: 11\n        }, this), dialogModeComanda === 'edit' && /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleSubmitComanda,\n          variant: \"contained\",\n          sx: {\n            textTransform: 'none',\n            fontWeight: 500,\n            px: 3\n          },\n          children: \"Salva Modifiche\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1096,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1088,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 891,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(CreaComandaConCavi, {\n      cantiereId: cantiereId,\n      open: openCreaConCavi,\n      onClose: () => setOpenCreaConCavi(false),\n      onSuccess: (response, successMessage) => {\n        console.log('🎉 Comanda creata, aggiornamento interfaccia...');\n\n        // Mostra messaggio di successo se fornito\n        if (successMessage) {\n          // Potresti aggiungere qui un toast/snackbar per mostrare il messaggio\n          console.log('📢 Successo:', successMessage);\n        }\n\n        // Ricarica tutti i dati per aggiornare l'interfaccia\n        loadComande();\n        loadStatistiche();\n        loadResponsabili();\n        setOpenCreaConCavi(false);\n        console.log('✅ Interfaccia aggiornata');\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1112,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 406,\n    columnNumber: 5\n  }, this);\n};\n_s(ComandeListRivoluzionato, \"MNrjrhRguytTreD3fQ3qyg9bcoI=\", false, function () {\n  return [useSearchParams];\n});\n_c = ComandeListRivoluzionato;\nexport default ComandeListRivoluzionato;\nvar _c;\n$RefreshReg$(_c, \"ComandeListRivoluzionato\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useSearchParams", "Box", "Typography", "<PERSON><PERSON>", "Paper", "Chip", "IconButton", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "TextField", "<PERSON><PERSON>", "CircularProgress", "<PERSON><PERSON><PERSON>", "List", "ListItem", "ListItemText", "Accordion", "AccordionSummary", "AccordionDetails", "<PERSON><PERSON>", "MenuItem", "Divider", "Add", "AddIcon", "Edit", "EditIcon", "Delete", "DeleteIcon", "Assignment", "AssignIcon", "Person", "PersonIcon", "Email", "EmailIcon", "Phone", "PhoneIcon", "ExpandMore", "ExpandMoreIcon", "CheckCircle", "CheckCircleIcon", "Verified", "VerifiedIcon", "Visibility", "ViewIcon", "comandeService", "responsabiliService", "CreaComandaConCavi", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ComandeListRivoluzionato", "cantiereId", "cantiereName", "_s", "searchParams", "setSearchParams", "loading", "setLoading", "error", "setError", "searchingComanda", "setSearchingComanda", "statistiche", "setStatistiche", "openCreaConCavi", "setOpenCreaConCavi", "responsabili", "setResponsabili", "loadingResponsabili", "setLoadingResponsabili", "comandePerResponsabile", "setComandePerResponsabile", "openResponsabileDialog", "setOpenResponsabileDialog", "dialogModeResponsabile", "setDialogModeResponsabile", "selectedResponsabile", "setSelectedResponsabile", "formDataResponsabile", "setFormDataResponsabile", "nome_responsabile", "email", "telefono", "openComandaDialog", "setOpenComandaDialog", "dialogModeComanda", "setDialogModeComanda", "selectedComanda", "setSelectedComanda", "formDataComanda", "setFormDataComanda", "tipo_comanda", "descrizione", "responsabile", "data_scadenza", "priorita", "note_capo_cantiere", "loadComande", "err", "console", "loadStatistiche", "stats", "getStatisticheComande", "loadResponsabili", "data", "getResponsabiliCantiere", "loadComandePerResponsabili", "_err$response", "_err$response$data", "errorMessage", "response", "detail", "message", "comandaParam", "get", "log", "length", "Object", "keys", "comandaTrovata", "comandeResp", "id_responsabile", "comandeTrovata", "find", "c", "codice_comanda", "handleOpenComandaDialog", "setTimeout", "prev", "newParams", "URLSearchParams", "delete", "warn", "for<PERSON>ach", "resp", "comande", "cmd", "responsabiliList", "comandeMap", "getComandeByResponsabile", "Array", "isArray", "handleOpenResponsabileDialog", "mode", "handleCloseResponsabileDialog", "handleSubmitResponsabile", "trim", "createResponsabile", "updateResponsabile", "handleDeleteResponsabile", "idResponsabile", "window", "confirm", "deleteResponsabile", "comanda", "handleCloseComandaDialog", "handleSubmitComanda", "updateComanda", "handleDeleteComanda", "codiceComanda", "deleteComanda", "getTipoComandaLabel", "tipo", "labels", "getStatoColor", "stato", "colors", "getPrioritaColor", "display", "justifyContent", "alignItems", "minHeight", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sx", "p", "mb", "variant", "fontWeight", "color", "severity", "bgcolor", "direction", "spacing", "flexWrap", "fontSize", "lineHeight", "responsabili_attivi", "totale_comande", "comande_in_corso", "comande_completate", "width", "height", "borderRadius", "Math", "round", "comande_create", "startIcon", "onClick", "textTransform", "px", "py", "backgroundColor", "border", "boxShadow", "elevation", "textAlign", "borderColor", "gutterBottom", "map", "expandIcon", "gap", "mt", "e", "stopPropagation", "icon", "label", "size", "clickable", "title", "pt", "dense", "divider", "primary", "secondary", "data_creazione", "Date", "toLocaleDateString", "numero_cavi_assegnati", "percentuale_completamento", "toFixed", "ml", "open", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "PaperProps", "pb", "value", "onChange", "target", "margin", "required", "type", "helperText", "select", "multiline", "rows", "InputLabelProps", "shrink", "onSuccess", "successMessage", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/components/comande/ComandeListRivoluzionato.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useSearchParams } from 'react-router-dom';\nimport {\n  Box,\n  Typography,\n  Button,\n  Paper,\n  Chip,\n  IconButton,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  TextField,\n  Alert,\n  CircularProgress,\n  Tooltip,\n  List,\n  ListItem,\n  ListItemText,\n  Accordion,\n  AccordionSummary,\n  AccordionDetails,\n  Stack,\n  MenuItem,\n  Divider\n} from '@mui/material';\nimport {\n  Add as AddIcon,\n  Edit as EditIcon,\n  Delete as DeleteIcon,\n  Assignment as AssignIcon,\n  Person as PersonIcon,\n  Email as EmailIcon,\n  Phone as PhoneIcon,\n  ExpandMore as ExpandMoreIcon,\n  CheckCircle as CheckCircleIcon,\n  Verified as VerifiedIcon,\n  Visibility as ViewIcon\n} from '@mui/icons-material';\nimport comandeService from '../../services/comandeService';\nimport responsabiliService from '../../services/responsabiliService';\nimport CreaComandaConCavi from './CreaComandaConCavi';\n\nconst ComandeListRivoluzionato = ({ cantiereId, cantiereName }) => {\n  // Hook per gestire i parametri URL\n  const [searchParams, setSearchParams] = useSearchParams();\n\n  // Stati principali - Responsabili come elemento principale\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [searchingComanda, setSearchingComanda] = useState(null);\n\n  // Stati comande\n  const [statistiche, setStatistiche] = useState(null);\n  const [openCreaConCavi, setOpenCreaConCavi] = useState(false);\n\n  // Stati responsabili\n  const [responsabili, setResponsabili] = useState([]);\n  const [loadingResponsabili, setLoadingResponsabili] = useState(false);\n  const [comandePerResponsabile, setComandePerResponsabile] = useState({});\n  const [openResponsabileDialog, setOpenResponsabileDialog] = useState(false);\n  const [dialogModeResponsabile, setDialogModeResponsabile] = useState('create');\n  const [selectedResponsabile, setSelectedResponsabile] = useState(null);\n  const [formDataResponsabile, setFormDataResponsabile] = useState({\n    nome_responsabile: '',\n    email: '',\n    telefono: ''\n  });\n\n  // Stati per dialog comande\n  const [openComandaDialog, setOpenComandaDialog] = useState(false);\n  const [dialogModeComanda, setDialogModeComanda] = useState('view'); // 'view', 'edit'\n  const [selectedComanda, setSelectedComanda] = useState(null);\n  const [formDataComanda, setFormDataComanda] = useState({\n    tipo_comanda: 'POSA',\n    descrizione: '',\n    responsabile: '',\n    data_scadenza: '',\n    priorita: 'NORMALE',\n    note_capo_cantiere: ''\n  });\n\n  const loadComande = async () => {\n    try {\n      setLoading(true);\n      // Non serve più salvare le comande in uno stato separato\n      // Le comande vengono caricate per responsabile\n      setError(null);\n    } catch (err) {\n      console.error('Errore nel caricamento delle comande:', err);\n      setError('Errore nel caricamento delle comande');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const loadStatistiche = async () => {\n    try {\n      const stats = await comandeService.getStatisticheComande(cantiereId);\n      setStatistiche(stats);\n    } catch (err) {\n      console.error('Errore nel caricamento delle statistiche:', err);\n    }\n  };\n\n  const loadResponsabili = async () => {\n    try {\n      setLoadingResponsabili(true);\n      setError(null);\n\n      const data = await responsabiliService.getResponsabiliCantiere(cantiereId);\n      setResponsabili(data || []);\n      await loadComandePerResponsabili(data || []);\n    } catch (err) {\n      console.error('Errore nel caricamento dei responsabili:', err);\n      const errorMessage = err.response?.data?.detail || err.message || 'Errore nel caricamento dei responsabili';\n      setError(`Errore nel caricamento dei responsabili: ${errorMessage}`);\n    } finally {\n      setLoadingResponsabili(false);\n    }\n  };\n\n  // Carica dati al mount - Focus sui responsabili\n  useEffect(() => {\n    if (cantiereId) {\n      loadResponsabili();\n      loadComande();\n      loadStatistiche();\n    }\n  }, [cantiereId]);\n\n  // Gestione parametro URL per aprire comanda specifica\n  useEffect(() => {\n    const comandaParam = searchParams.get('comanda');\n    console.log('🔍 Controllo parametro URL comanda:', comandaParam);\n    console.log('📊 Stato dati:', {\n      responsabili: responsabili.length,\n      comandePerResponsabile: Object.keys(comandePerResponsabile).length,\n      loading,\n      loadingResponsabili\n    });\n\n    // Imposta lo stato di ricerca\n    if (comandaParam && comandaParam !== searchingComanda) {\n      setSearchingComanda(comandaParam);\n    }\n\n    if (comandaParam && responsabili.length > 0 && Object.keys(comandePerResponsabile).length > 0) {\n      console.log('🔎 Ricerca comanda tra i responsabili...');\n\n      // Cerca la comanda tra tutti i responsabili\n      let comandaTrovata = null;\n\n      for (const responsabile of responsabili) {\n        const comandeResp = comandePerResponsabile[responsabile.id_responsabile] || [];\n        console.log(`📋 Responsabile ${responsabile.nome_responsabile}: ${comandeResp.length} comande`);\n        comandeTrovata = comandeResp.find(c => c.codice_comanda === comandaParam);\n        if (comandaTrovata) {\n          console.log('✅ Comanda trovata:', comandaTrovata);\n          break;\n        }\n      }\n\n      if (comandaTrovata) {\n        console.log('🎯 Apertura automatica comanda da URL:', comandaParam);\n        setSearchingComanda(null); // Rimuovi lo stato di ricerca\n        handleOpenComandaDialog('view', comandaTrovata);\n        // Rimuovi il parametro dall'URL per evitare riaperture\n        setTimeout(() => {\n          setSearchParams(prev => {\n            const newParams = new URLSearchParams(prev);\n            newParams.delete('comanda');\n            return newParams;\n          });\n        }, 100);\n      } else {\n        console.warn('⚠️ Comanda non trovata:', comandaParam);\n        console.log('📋 Comande disponibili:');\n        responsabili.forEach(resp => {\n          const comande = comandePerResponsabile[resp.id_responsabile] || [];\n          comande.forEach(cmd => {\n            console.log(`  - ${cmd.codice_comanda} (${resp.nome_responsabile})`);\n          });\n        });\n\n        // Se non troviamo la comanda, potrebbe essere che i dati non sono completi\n        // Riprova dopo un breve delay\n        if (!loading && !loadingResponsabili) {\n          console.log('🔄 Tentativo di ricaricamento dati...');\n          setTimeout(() => {\n            loadResponsabili();\n          }, 500);\n        }\n      }\n    } else if (comandaParam) {\n      console.log('⏳ Dati non ancora caricati, attendo...');\n\n      // Se abbiamo un parametro ma i dati non sono caricati, forza il caricamento\n      if (!loading && !loadingResponsabili && responsabili.length === 0) {\n        console.log('🚀 Forzatura caricamento responsabili...');\n        loadResponsabili();\n      }\n    }\n  }, [searchParams, responsabili, comandePerResponsabile, loading, loadingResponsabili]);\n\n  const loadComandePerResponsabili = async (responsabiliList) => {\n    try {\n      const comandeMap = {};\n      for (const responsabile of responsabiliList) {\n        try {\n          const response = await comandeService.getComandeByResponsabile(cantiereId, responsabile.nome_responsabile);\n          // Assicurati che sia sempre un array\n          let comande = [];\n          if (response && Array.isArray(response)) {\n            comande = response;\n          } else if (response && response.comande && Array.isArray(response.comande)) {\n            comande = response.comande;\n          } else if (response && response.data && Array.isArray(response.data)) {\n            comande = response.data;\n          }\n          comandeMap[responsabile.id_responsabile] = comande;\n        } catch (err) {\n          console.error(`Errore nel caricamento comande per ${responsabile.nome_responsabile}:`, err);\n          comandeMap[responsabile.id_responsabile] = [];\n        }\n      }\n      setComandePerResponsabile(comandeMap);\n    } catch (err) {\n      console.error('Errore nel caricamento delle comande:', err);\n    }\n  };\n\n  // Gestione responsabili\n  const handleOpenResponsabileDialog = (mode, responsabile = null) => {\n    setDialogModeResponsabile(mode);\n    setSelectedResponsabile(responsabile);\n    \n    if (mode === 'edit' && responsabile) {\n      setFormDataResponsabile({\n        nome_responsabile: responsabile.nome_responsabile || '',\n        email: responsabile.email || '',\n        telefono: responsabile.telefono || ''\n      });\n    } else {\n      setFormDataResponsabile({\n        nome_responsabile: '',\n        email: '',\n        telefono: ''\n      });\n    }\n    \n    setOpenResponsabileDialog(true);\n  };\n\n  const handleCloseResponsabileDialog = () => {\n    setOpenResponsabileDialog(false);\n    setSelectedResponsabile(null);\n    setError(null);\n  };\n\n  const handleSubmitResponsabile = async () => {\n    try {\n      setError(null);\n      \n      if (!formDataResponsabile.nome_responsabile.trim()) {\n        setError('Il nome del responsabile è obbligatorio');\n        return;\n      }\n      \n      if (!formDataResponsabile.email && !formDataResponsabile.telefono) {\n        setError('Almeno uno tra email e telefono deve essere specificato');\n        return;\n      }\n\n      if (dialogModeResponsabile === 'create') {\n        await responsabiliService.createResponsabile(cantiereId, formDataResponsabile);\n      } else if (dialogModeResponsabile === 'edit') {\n        await responsabiliService.updateResponsabile(selectedResponsabile.id_responsabile, formDataResponsabile);\n      }\n\n      handleCloseResponsabileDialog();\n      await loadResponsabili();\n    } catch (err) {\n      console.error('Errore nel salvataggio:', err);\n      setError(err.detail || 'Errore nel salvataggio del responsabile');\n    }\n  };\n\n  const handleDeleteResponsabile = async (idResponsabile) => {\n    if (!window.confirm('Sei sicuro di voler eliminare questo responsabile?')) {\n      return;\n    }\n\n    try {\n      await responsabiliService.deleteResponsabile(idResponsabile);\n      await loadResponsabili();\n    } catch (err) {\n      console.error('Errore nell\\'eliminazione:', err);\n      setError('Errore nell\\'eliminazione del responsabile');\n    }\n  };\n\n  // Gestione comande\n  const handleOpenComandaDialog = (mode, comanda = null) => {\n    setDialogModeComanda(mode);\n    setSelectedComanda(comanda);\n\n    if (mode === 'edit' && comanda) {\n      setFormDataComanda({\n        tipo_comanda: comanda.tipo_comanda,\n        descrizione: comanda.descrizione || '',\n        responsabile: comanda.responsabile || '',\n        data_scadenza: comanda.data_scadenza || '',\n        priorita: comanda.priorita || 'NORMALE',\n        note_capo_cantiere: comanda.note_capo_cantiere || ''\n      });\n    }\n\n    setOpenComandaDialog(true);\n  };\n\n  const handleCloseComandaDialog = () => {\n    setOpenComandaDialog(false);\n    setSelectedComanda(null);\n    setFormDataComanda({\n      tipo_comanda: 'POSA',\n      descrizione: '',\n      responsabile: '',\n      data_scadenza: '',\n      priorita: 'NORMALE',\n      note_capo_cantiere: ''\n    });\n  };\n\n  const handleSubmitComanda = async () => {\n    try {\n      if (dialogModeComanda === 'edit') {\n        await comandeService.updateComanda(selectedComanda.codice_comanda, formDataComanda);\n        handleCloseComandaDialog();\n        await loadResponsabili(); // Ricarica per aggiornare le comande\n        await loadStatistiche();\n      }\n    } catch (err) {\n      console.error('Errore nel salvataggio:', err);\n      setError('Errore nel salvataggio della comanda');\n    }\n  };\n\n  const handleDeleteComanda = async (codiceComanda) => {\n    if (!window.confirm('Sei sicuro di voler eliminare questa comanda?')) {\n      return;\n    }\n\n    try {\n      await comandeService.deleteComanda(codiceComanda);\n      await loadResponsabili(); // Ricarica per aggiornare le comande\n      await loadStatistiche();\n    } catch (err) {\n      console.error('Errore nell\\'eliminazione:', err);\n      setError('Errore nell\\'eliminazione della comanda');\n    }\n  };\n\n  const getTipoComandaLabel = (tipo) => {\n    const labels = {\n      'POSA': 'Posa',\n      'COLLEGAMENTO_PARTENZA': 'Coll. Partenza',\n      'COLLEGAMENTO_ARRIVO': 'Coll. Arrivo',\n      'CERTIFICAZIONE': 'Certificazione',\n      'TESTING': 'Testing/Certificazione'\n    };\n    return labels[tipo] || tipo;\n  };\n\n  const getStatoColor = (stato) => {\n    const colors = {\n      'CREATA': 'default',\n      'ASSEGNATA': 'primary',\n      'IN_CORSO': 'warning',\n      'COMPLETATA': 'success',\n      'ANNULLATA': 'error'\n    };\n    return colors[stato] || 'default';\n  };\n\n  const getPrioritaColor = (priorita) => {\n    const colors = {\n      'BASSA': 'default',\n      'NORMALE': 'primary',\n      'ALTA': 'warning',\n      'URGENTE': 'error'\n    };\n    return colors[priorita] || 'default';\n  };\n\n  if (loading) {\n    return (\n      <Box display=\"flex\" justifyContent=\"center\" alignItems=\"center\" minHeight=\"400px\">\n        <CircularProgress />\n      </Box>\n    );\n  }\n\n  return (\n    <Box sx={{ p: 3 }}>\n      {/* Header */}\n      <Box mb={3}>\n        <Typography variant=\"h4\" sx={{ fontWeight: 600, color: 'primary.main' }}>\n          {cantiereName}\n        </Typography>\n      </Box>\n\n      {error && (\n        <Alert severity=\"error\" sx={{ mb: 2 }}>\n          {error}\n        </Alert>\n      )}\n\n      {searchingComanda && (\n        <Alert severity=\"info\" sx={{ mb: 2 }}>\n          🔍 Ricerca comanda {searchingComanda} in corso...\n        </Alert>\n      )}\n\n      {/* Statistiche in stile Visualizza Cavi */}\n      {statistiche && (\n        <Paper sx={{ p: 2, mb: 3, bgcolor: 'grey.50' }}>\n          <Stack direction=\"row\" spacing={4} alignItems=\"center\" justifyContent=\"space-between\" flexWrap=\"wrap\">\n            {/* Responsabili */}\n            <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n              <PersonIcon color=\"primary\" fontSize=\"small\" />\n              <Box>\n                <Typography variant=\"h6\" fontWeight=\"bold\" sx={{ lineHeight: 1 }}>\n                  {statistiche.responsabili_attivi || 0}\n                </Typography>\n                <Typography variant=\"caption\" color=\"text.secondary\">\n                  Responsabili\n                </Typography>\n              </Box>\n            </Stack>\n\n            {/* Totale Comande */}\n            <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n              <AssignIcon color=\"info\" fontSize=\"small\" />\n              <Box>\n                <Typography variant=\"h6\" fontWeight=\"bold\" sx={{ lineHeight: 1 }}>\n                  {statistiche.totale_comande || 0}\n                </Typography>\n                <Typography variant=\"caption\" color=\"text.secondary\">\n                  Totale\n                </Typography>\n              </Box>\n            </Stack>\n\n            {/* In Corso */}\n            <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n              <CheckCircleIcon color=\"warning\" fontSize=\"small\" />\n              <Box>\n                <Typography variant=\"h6\" fontWeight=\"bold\" sx={{ lineHeight: 1 }}>\n                  {statistiche.comande_in_corso || 0}\n                </Typography>\n                <Typography variant=\"caption\" color=\"text.secondary\">\n                  In Corso\n                </Typography>\n              </Box>\n            </Stack>\n\n            {/* Completate */}\n            <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n              <VerifiedIcon color=\"success\" fontSize=\"small\" />\n              <Box>\n                <Typography variant=\"h6\" fontWeight=\"bold\" sx={{ lineHeight: 1 }}>\n                  {statistiche.comande_completate || 0}\n                </Typography>\n                <Typography variant=\"caption\" color=\"text.secondary\">\n                  Completate\n                </Typography>\n              </Box>\n            </Stack>\n\n            {/* Percentuale completamento */}\n            <Stack direction=\"row\" alignItems=\"center\" spacing={1}>\n              <Box sx={{\n                width: 32,\n                height: 32,\n                borderRadius: '50%',\n                bgcolor: (statistiche.comande_completate / (statistiche.totale_comande || 1)) >= 0.8 ? 'success.main' :\n                         (statistiche.comande_completate / (statistiche.totale_comande || 1)) >= 0.5 ? 'warning.main' : 'error.main',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center'\n              }}>\n                <Typography variant=\"caption\" fontWeight=\"bold\" color=\"white\">\n                  {statistiche.totale_comande > 0 ? Math.round((statistiche.comande_completate / statistiche.totale_comande) * 100) : 0}%\n                </Typography>\n              </Box>\n              <Box>\n                <Typography variant=\"body2\" fontWeight=\"medium\" sx={{ lineHeight: 1 }}>\n                  Completamento\n                </Typography>\n                <Typography variant=\"caption\" color=\"text.secondary\">\n                  {statistiche.comande_create || 0} create\n                </Typography>\n              </Box>\n            </Stack>\n          </Stack>\n        </Paper>\n      )}\n\n      {/* Sezione Responsabili - Elemento Principale */}\n      <Box>\n        <Box>\n          {/* Toolbar Responsabili */}\n          <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\" mb={3}>\n            <Typography variant=\"h5\" sx={{ fontWeight: 500, color: 'text.primary' }}>\n              Responsabili del Cantiere\n            </Typography>\n            <Button\n              variant=\"contained\"\n              startIcon={<AddIcon />}\n              onClick={() => handleOpenResponsabileDialog('create')}\n              sx={{\n                textTransform: 'none',\n                fontWeight: 500,\n                px: 3,\n                py: 1,\n                backgroundColor: '#f8f9fa',\n                color: '#212529',\n                border: '1px solid #dee2e6',\n                boxShadow: 'none',\n                '&:hover': {\n                  backgroundColor: '#e9ecef',\n                  boxShadow: 'none'\n                }\n              }}\n            >\n              Inserisci Responsabile\n            </Button>\n          </Box>\n\n          {loadingResponsabili ? (\n            <Box display=\"flex\" justifyContent=\"center\" py={4}>\n              <CircularProgress />\n            </Box>\n          ) : (\n            <Box>\n              {responsabili.length === 0 ? (\n                <Paper\n                  elevation={0}\n                  sx={{\n                    p: 6,\n                    textAlign: 'center',\n                    backgroundColor: 'grey.50',\n                    border: '1px dashed',\n                    borderColor: 'grey.300'\n                  }}\n                >\n                  <PersonIcon sx={{ fontSize: 48, color: 'grey.400', mb: 2 }} />\n                  <Typography variant=\"h6\" color=\"text.secondary\" gutterBottom>\n                    Nessun responsabile configurato\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 3 }}>\n                    Aggiungi il primo responsabile per iniziare a gestire le comande\n                  </Typography>\n                  <Button\n                    variant=\"contained\"\n                    startIcon={<AddIcon />}\n                    onClick={() => handleOpenResponsabileDialog('create')}\n                    sx={{\n                      textTransform: 'none',\n                      backgroundColor: '#f8f9fa',\n                      color: '#212529',\n                      border: '1px solid #dee2e6',\n                      boxShadow: 'none',\n                      '&:hover': {\n                        backgroundColor: '#e9ecef',\n                        boxShadow: 'none'\n                      }\n                    }}\n                  >\n                    Inserisci Primo Responsabile\n                  </Button>\n                </Paper>\n              ) : (\n                responsabili.map((responsabile) => (\n                  <Accordion\n                    key={responsabile.id_responsabile}\n                    sx={{\n                      mb: 2,\n                      '&:before': { display: 'none' },\n                      boxShadow: '0 1px 3px rgba(0,0,0,0.1)',\n                      border: '1px solid',\n                      borderColor: 'grey.200'\n                    }}\n                  >\n                    <AccordionSummary\n                      expandIcon={<ExpandMoreIcon />}\n                      sx={{\n                        '&:hover': {\n                          backgroundColor: 'grey.50'\n                        }\n                      }}\n                    >\n                      <Box display=\"flex\" alignItems=\"center\" justifyContent=\"space-between\" width=\"100%\">\n                        <Box display=\"flex\" alignItems=\"center\" gap={2}>\n                          <PersonIcon color=\"primary\" sx={{ fontSize: 28 }} />\n                          <Box>\n                            <Typography variant=\"h6\" sx={{ fontWeight: 500 }}>\n                              {responsabile.nome_responsabile}\n                            </Typography>\n                            <Box display=\"flex\" gap={3} mt={0.5}>\n                              {responsabile.email && (\n                                <Box display=\"flex\" alignItems=\"center\" gap={0.5}>\n                                  <EmailIcon fontSize=\"small\" color=\"action\" />\n                                  <Typography variant=\"body2\" color=\"text.secondary\">\n                                    {responsabile.email}\n                                  </Typography>\n                                </Box>\n                              )}\n                              {responsabile.telefono && (\n                                <Box display=\"flex\" alignItems=\"center\" gap={0.5}>\n                                  <PhoneIcon fontSize=\"small\" color=\"action\" />\n                                  <Typography variant=\"body2\" color=\"text.secondary\">\n                                    {responsabile.telefono}\n                                  </Typography>\n                                </Box>\n                              )}\n                            </Box>\n                          </Box>\n                        </Box>\n\n                        <Box display=\"flex\" alignItems=\"center\" gap={1} onClick={(e) => e.stopPropagation()}>\n                          <Chip\n                            icon={<AssignIcon />}\n                            label={`${Array.isArray(comandePerResponsabile[responsabile.id_responsabile]) ? comandePerResponsabile[responsabile.id_responsabile].length : 0} comande`}\n                            size=\"small\"\n                            color=\"primary\"\n                            variant=\"outlined\"\n                            clickable\n                            onClick={() => {\n                              // Pre-seleziona il responsabile nel dialog di creazione comanda\n                              setOpenCreaConCavi(true);\n                            }}\n                            sx={{\n                              fontWeight: 500,\n                              backgroundColor: 'rgba(33, 150, 243, 0.1)',\n                              color: '#1976d2',\n                              border: '1px solid rgba(33, 150, 243, 0.3)',\n                              '&:hover': {\n                                backgroundColor: 'rgba(33, 150, 243, 0.2) !important',\n                                color: '#1565c0 !important'\n                              }\n                            }}\n                          />\n                          <Tooltip title=\"Modifica responsabile\">\n                            <IconButton\n                              size=\"small\"\n                              onClick={() => handleOpenResponsabileDialog('edit', responsabile)}\n                              sx={{\n                                color: '#6c757d',\n                                '&:hover': {\n                                  backgroundColor: '#e9ecef'\n                                }\n                              }}\n                            >\n                              <EditIcon fontSize=\"small\" />\n                            </IconButton>\n                          </Tooltip>\n                          <Tooltip title=\"Elimina responsabile\">\n                            <IconButton\n                              size=\"small\"\n                              onClick={() => handleDeleteResponsabile(responsabile.id_responsabile)}\n                              sx={{\n                                color: '#6c757d',\n                                '&:hover': {\n                                  backgroundColor: '#e9ecef'\n                                }\n                              }}\n                            >\n                              <DeleteIcon fontSize=\"small\" />\n                            </IconButton>\n                          </Tooltip>\n                        </Box>\n                      </Box>\n                    </AccordionSummary>\n\n                    <AccordionDetails sx={{ pt: 2 }}>\n                      <Typography variant=\"subtitle2\" gutterBottom sx={{ fontWeight: 500, mb: 2 }}>\n                        Comande Assegnate\n                      </Typography>\n\n                      {(!Array.isArray(comandePerResponsabile[responsabile.id_responsabile]) || comandePerResponsabile[responsabile.id_responsabile].length === 0) ? (\n                        <Box\n                          sx={{\n                            p: 3,\n                            textAlign: 'center',\n                            backgroundColor: 'grey.50',\n                            borderRadius: 1,\n                            border: '1px dashed',\n                            borderColor: 'grey.300'\n                          }}\n                        >\n                          <Typography variant=\"body2\" color=\"text.secondary\">\n                            Nessuna comanda assegnata a questo responsabile\n                          </Typography>\n                        </Box>\n                      ) : (\n                        <List dense>\n                          {Array.isArray(comandePerResponsabile[responsabile.id_responsabile]) && comandePerResponsabile[responsabile.id_responsabile].map((comanda) => (\n                            <ListItem\n                              key={comanda.codice_comanda}\n                              divider\n                              sx={{\n                                '&:hover': {\n                                  backgroundColor: 'rgba(33, 150, 243, 0.1)'\n                                }\n                              }}\n                            >\n                              <ListItemText\n                                primary={\n                                  <Box display=\"flex\" alignItems=\"center\" gap={1}>\n                                    <Typography variant=\"body2\" fontWeight=\"bold\">\n                                      {comanda.codice_comanda}\n                                    </Typography>\n                                    <Chip\n                                      label={getTipoComandaLabel(comanda.tipo_comanda)}\n                                      size=\"small\"\n                                      variant=\"outlined\"\n                                    />\n                                    <Chip\n                                      label={comanda.stato || 'CREATA'}\n                                      size=\"small\"\n                                      color={getStatoColor(comanda.stato)}\n                                    />\n                                    {comanda.priorita && comanda.priorita !== 'NORMALE' && (\n                                      <Chip\n                                        label={comanda.priorita}\n                                        size=\"small\"\n                                        color={getPrioritaColor(comanda.priorita)}\n                                        variant=\"filled\"\n                                      />\n                                    )}\n                                  </Box>\n                                }\n                                secondary={\n                                  <Box>\n                                    <Typography variant=\"body2\" color=\"textSecondary\">\n                                      {comanda.descrizione || 'Nessuna descrizione'}\n                                      {comanda.data_creazione && ` • Creata: ${new Date(comanda.data_creazione).toLocaleDateString()}`}\n                                    </Typography>\n                                    {comanda.numero_cavi_assegnati > 0 && (\n                                      <Typography variant=\"caption\" color=\"primary\">\n                                        {comanda.numero_cavi_assegnati} cavi assegnati\n                                        {comanda.percentuale_completamento && ` • ${comanda.percentuale_completamento.toFixed(1)}% completato`}\n                                      </Typography>\n                                    )}\n                                  </Box>\n                                }\n                              />\n                              <Box display=\"flex\" gap={0.5} ml={1}>\n                                <Tooltip title=\"Visualizza\">\n                                  <IconButton\n                                    size=\"small\"\n                                    onClick={() => handleOpenComandaDialog('view', comanda)}\n                                    sx={{\n                                      color: '#6c757d',\n                                      '&:hover': {\n                                        backgroundColor: '#e9ecef'\n                                      }\n                                    }}\n                                  >\n                                    <ViewIcon fontSize=\"small\" />\n                                  </IconButton>\n                                </Tooltip>\n                                <Tooltip title=\"Modifica\">\n                                  <IconButton\n                                    size=\"small\"\n                                    onClick={() => handleOpenComandaDialog('edit', comanda)}\n                                    sx={{\n                                      color: '#6c757d',\n                                      '&:hover': {\n                                        backgroundColor: '#e9ecef'\n                                      }\n                                    }}\n                                  >\n                                    <EditIcon fontSize=\"small\" />\n                                  </IconButton>\n                                </Tooltip>\n                                <Tooltip title=\"Elimina\">\n                                  <IconButton\n                                    size=\"small\"\n                                    onClick={() => handleDeleteComanda(comanda.codice_comanda)}\n                                    sx={{\n                                      color: '#6c757d',\n                                      '&:hover': {\n                                        backgroundColor: '#e9ecef'\n                                      }\n                                    }}\n                                  >\n                                    <DeleteIcon fontSize=\"small\" />\n                                  </IconButton>\n                                </Tooltip>\n                              </Box>\n                            </ListItem>\n                          ))}\n                        </List>\n                      )}\n                    </AccordionDetails>\n                  </Accordion>\n                ))\n              )}\n            </Box>\n          )}\n        </Box>\n      </Box>\n\n      {/* Dialog per creazione/modifica responsabile */}\n      <Dialog\n        open={openResponsabileDialog}\n        onClose={handleCloseResponsabileDialog}\n        maxWidth=\"sm\"\n        fullWidth\n        PaperProps={{\n          sx: { borderRadius: 2 }\n        }}\n      >\n        <DialogTitle sx={{ pb: 1 }}>\n          <Typography variant=\"h6\" sx={{ fontWeight: 600 }}>\n            {dialogModeResponsabile === 'create' ? 'Inserisci Responsabile' : 'Modifica Responsabile'}\n          </Typography>\n        </DialogTitle>\n        <DialogContent>\n          <Box sx={{ pt: 2 }}>\n            <TextField\n              fullWidth\n              label=\"Nome Responsabile\"\n              value={formDataResponsabile.nome_responsabile}\n              onChange={(e) => setFormDataResponsabile({ ...formDataResponsabile, nome_responsabile: e.target.value })}\n              margin=\"normal\"\n              required\n              variant=\"outlined\"\n              sx={{ mb: 2 }}\n            />\n\n            <TextField\n              fullWidth\n              label=\"Email\"\n              type=\"email\"\n              value={formDataResponsabile.email}\n              onChange={(e) => setFormDataResponsabile({ ...formDataResponsabile, email: e.target.value })}\n              margin=\"normal\"\n              variant=\"outlined\"\n              helperText=\"Email per notifiche (opzionale se inserisci telefono)\"\n              sx={{ mb: 2 }}\n            />\n\n            <TextField\n              fullWidth\n              label=\"Telefono\"\n              value={formDataResponsabile.telefono}\n              onChange={(e) => setFormDataResponsabile({ ...formDataResponsabile, telefono: e.target.value })}\n              margin=\"normal\"\n              variant=\"outlined\"\n              helperText=\"Numero per SMS (opzionale se inserisci email)\"\n            />\n          </Box>\n        </DialogContent>\n        <DialogActions sx={{ p: 3, pt: 2 }}>\n          <Button\n            onClick={handleCloseResponsabileDialog}\n            sx={{ textTransform: 'none' }}\n          >\n            Annulla\n          </Button>\n          <Button\n            onClick={handleSubmitResponsabile}\n            variant=\"contained\"\n            sx={{\n              textTransform: 'none',\n              fontWeight: 500,\n              px: 3\n            }}\n          >\n            {dialogModeResponsabile === 'create' ? 'Crea' : 'Salva'}\n          </Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* Dialog per visualizzazione/modifica comanda */}\n      <Dialog\n        open={openComandaDialog}\n        onClose={handleCloseComandaDialog}\n        maxWidth=\"md\"\n        fullWidth\n        PaperProps={{\n          sx: { borderRadius: 2 }\n        }}\n      >\n        <DialogTitle sx={{ pb: 1 }}>\n          <Typography variant=\"h6\" sx={{ fontWeight: 600 }}>\n            {dialogModeComanda === 'view' ? 'Dettagli Comanda' : 'Modifica Comanda'}\n          </Typography>\n        </DialogTitle>\n        <DialogContent>\n          <Box sx={{ pt: 2 }}>\n            {dialogModeComanda === 'view' && selectedComanda ? (\n              <List>\n                <ListItem>\n                  <ListItemText\n                    primary=\"Codice Comanda\"\n                    secondary={selectedComanda.codice_comanda}\n                  />\n                </ListItem>\n                <Divider />\n                <ListItem>\n                  <ListItemText\n                    primary=\"Tipo\"\n                    secondary={getTipoComandaLabel(selectedComanda.tipo_comanda)}\n                  />\n                </ListItem>\n                <Divider />\n                <ListItem>\n                  <ListItemText\n                    primary=\"Stato\"\n                    secondary={\n                      <Chip\n                        label={selectedComanda.stato}\n                        color={getStatoColor(selectedComanda.stato)}\n                        size=\"small\"\n                      />\n                    }\n                  />\n                </ListItem>\n                <Divider />\n                <ListItem>\n                  <ListItemText\n                    primary=\"Descrizione\"\n                    secondary={selectedComanda.descrizione || 'Nessuna descrizione'}\n                  />\n                </ListItem>\n                <Divider />\n                <ListItem>\n                  <ListItemText\n                    primary=\"Priorità\"\n                    secondary={\n                      <Chip\n                        label={selectedComanda.priorita || 'NORMALE'}\n                        color={getPrioritaColor(selectedComanda.priorita || 'NORMALE')}\n                        size=\"small\"\n                      />\n                    }\n                  />\n                </ListItem>\n                <Divider />\n                <ListItem>\n                  <ListItemText\n                    primary=\"Responsabile\"\n                    secondary={selectedComanda.responsabile || 'Non assegnato'}\n                  />\n                </ListItem>\n                {selectedComanda.note_capo_cantiere && (\n                  <>\n                    <Divider />\n                    <ListItem>\n                      <ListItemText\n                        primary=\"Note Capo Cantiere\"\n                        secondary={selectedComanda.note_capo_cantiere}\n                      />\n                    </ListItem>\n                  </>\n                )}\n                <Divider />\n                <ListItem>\n                  <ListItemText\n                    primary=\"Data Creazione\"\n                    secondary={new Date(selectedComanda.data_creazione).toLocaleDateString('it-IT')}\n                  />\n                </ListItem>\n                {selectedComanda.data_scadenza && (\n                  <>\n                    <Divider />\n                    <ListItem>\n                      <ListItemText\n                        primary=\"Data Scadenza\"\n                        secondary={new Date(selectedComanda.data_scadenza).toLocaleDateString('it-IT')}\n                      />\n                    </ListItem>\n                  </>\n                )}\n                <Divider />\n                <ListItem>\n                  <ListItemText\n                    primary=\"Cavi Assegnati\"\n                    secondary={selectedComanda.numero_cavi_assegnati || 0}\n                  />\n                </ListItem>\n                <Divider />\n                <ListItem>\n                  <ListItemText\n                    primary=\"Completamento\"\n                    secondary={`${(selectedComanda.percentuale_completamento || 0).toFixed(1)}%`}\n                  />\n                </ListItem>\n              </List>\n            ) : (\n              <>\n                <TextField\n                  fullWidth\n                  select\n                  label=\"Tipo Comanda\"\n                  value={formDataComanda.tipo_comanda}\n                  onChange={(e) => setFormDataComanda({ ...formDataComanda, tipo_comanda: e.target.value })}\n                  margin=\"normal\"\n                  sx={{ mb: 2 }}\n                >\n                  <MenuItem value=\"POSA\">Posa</MenuItem>\n                  <MenuItem value=\"COLLEGAMENTO_PARTENZA\">Collegamento Partenza</MenuItem>\n                  <MenuItem value=\"COLLEGAMENTO_ARRIVO\">Collegamento Arrivo</MenuItem>\n                  <MenuItem value=\"CERTIFICAZIONE\">Certificazione</MenuItem>\n                  <MenuItem value=\"TESTING\">Testing</MenuItem>\n                </TextField>\n\n                <TextField\n                  fullWidth\n                  select\n                  label=\"Priorità\"\n                  value={formDataComanda.priorita}\n                  onChange={(e) => setFormDataComanda({ ...formDataComanda, priorita: e.target.value })}\n                  margin=\"normal\"\n                  sx={{ mb: 2 }}\n                >\n                  <MenuItem value=\"BASSA\">Bassa</MenuItem>\n                  <MenuItem value=\"NORMALE\">Normale</MenuItem>\n                  <MenuItem value=\"ALTA\">Alta</MenuItem>\n                  <MenuItem value=\"URGENTE\">Urgente</MenuItem>\n                </TextField>\n\n                <TextField\n                  fullWidth\n                  label=\"Descrizione\"\n                  value={formDataComanda.descrizione}\n                  onChange={(e) => setFormDataComanda({ ...formDataComanda, descrizione: e.target.value })}\n                  margin=\"normal\"\n                  multiline\n                  rows={3}\n                  sx={{ mb: 2 }}\n                />\n\n                <TextField\n                  fullWidth\n                  label=\"Responsabile\"\n                  value={formDataComanda.responsabile}\n                  onChange={(e) => setFormDataComanda({ ...formDataComanda, responsabile: e.target.value })}\n                  margin=\"normal\"\n                  required\n                  helperText=\"Chi eseguirà il lavoro (obbligatorio)\"\n                  sx={{ mb: 2 }}\n                />\n\n                <TextField\n                  fullWidth\n                  label=\"Note Capo Cantiere\"\n                  value={formDataComanda.note_capo_cantiere}\n                  onChange={(e) => setFormDataComanda({ ...formDataComanda, note_capo_cantiere: e.target.value })}\n                  margin=\"normal\"\n                  multiline\n                  rows={2}\n                  helperText=\"Istruzioni specifiche per il responsabile\"\n                  sx={{ mb: 2 }}\n                />\n\n                <TextField\n                  fullWidth\n                  label=\"Data Scadenza\"\n                  type=\"date\"\n                  value={formDataComanda.data_scadenza}\n                  onChange={(e) => setFormDataComanda({ ...formDataComanda, data_scadenza: e.target.value })}\n                  margin=\"normal\"\n                  InputLabelProps={{\n                    shrink: true,\n                  }}\n                />\n              </>\n            )}\n          </Box>\n        </DialogContent>\n        <DialogActions sx={{ p: 3, pt: 2 }}>\n          <Button\n            onClick={handleCloseComandaDialog}\n            sx={{ textTransform: 'none' }}\n          >\n            {dialogModeComanda === 'view' ? 'Chiudi' : 'Annulla'}\n          </Button>\n          {dialogModeComanda === 'edit' && (\n            <Button\n              onClick={handleSubmitComanda}\n              variant=\"contained\"\n              sx={{\n                textTransform: 'none',\n                fontWeight: 500,\n                px: 3\n              }}\n            >\n              Salva Modifiche\n            </Button>\n          )}\n        </DialogActions>\n      </Dialog>\n\n      {/* Dialog CreaComandaConCavi */}\n      <CreaComandaConCavi\n        cantiereId={cantiereId}\n        open={openCreaConCavi}\n        onClose={() => setOpenCreaConCavi(false)}\n        onSuccess={(response, successMessage) => {\n          console.log('🎉 Comanda creata, aggiornamento interfaccia...');\n\n          // Mostra messaggio di successo se fornito\n          if (successMessage) {\n            // Potresti aggiungere qui un toast/snackbar per mostrare il messaggio\n            console.log('📢 Successo:', successMessage);\n          }\n\n          // Ricarica tutti i dati per aggiornare l'interfaccia\n          loadComande();\n          loadStatistiche();\n          loadResponsabili();\n          setOpenCreaConCavi(false);\n\n          console.log('✅ Interfaccia aggiornata');\n        }}\n      />\n    </Box>\n  );\n};\n\nexport default ComandeListRivoluzionato;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,eAAe,QAAQ,kBAAkB;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,IAAI,EACJC,UAAU,EACVC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,SAAS,EACTC,KAAK,EACLC,gBAAgB,EAChBC,OAAO,EACPC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,SAAS,EACTC,gBAAgB,EAChBC,gBAAgB,EAChBC,KAAK,EACLC,QAAQ,EACRC,OAAO,QACF,eAAe;AACtB,SACEC,GAAG,IAAIC,OAAO,EACdC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,UAAU,IAAIC,UAAU,EACxBC,MAAM,IAAIC,UAAU,EACpBC,KAAK,IAAIC,SAAS,EAClBC,KAAK,IAAIC,SAAS,EAClBC,UAAU,IAAIC,cAAc,EAC5BC,WAAW,IAAIC,eAAe,EAC9BC,QAAQ,IAAIC,YAAY,EACxBC,UAAU,IAAIC,QAAQ,QACjB,qBAAqB;AAC5B,OAAOC,cAAc,MAAM,+BAA+B;AAC1D,OAAOC,mBAAmB,MAAM,oCAAoC;AACpE,OAAOC,kBAAkB,MAAM,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEtD,MAAMC,wBAAwB,GAAGA,CAAC;EAAEC,UAAU;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EACjE;EACA,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG1D,eAAe,CAAC,CAAC;;EAEzD;EACA,MAAM,CAAC2D,OAAO,EAAEC,UAAU,CAAC,GAAG9D,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC+D,KAAK,EAAEC,QAAQ,CAAC,GAAGhE,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACiE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGlE,QAAQ,CAAC,IAAI,CAAC;;EAE9D;EACA,MAAM,CAACmE,WAAW,EAAEC,cAAc,CAAC,GAAGpE,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACqE,eAAe,EAAEC,kBAAkB,CAAC,GAAGtE,QAAQ,CAAC,KAAK,CAAC;;EAE7D;EACA,MAAM,CAACuE,YAAY,EAAEC,eAAe,CAAC,GAAGxE,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACyE,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG1E,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAAC2E,sBAAsB,EAAEC,yBAAyB,CAAC,GAAG5E,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxE,MAAM,CAAC6E,sBAAsB,EAAEC,yBAAyB,CAAC,GAAG9E,QAAQ,CAAC,KAAK,CAAC;EAC3E,MAAM,CAAC+E,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGhF,QAAQ,CAAC,QAAQ,CAAC;EAC9E,MAAM,CAACiF,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGlF,QAAQ,CAAC,IAAI,CAAC;EACtE,MAAM,CAACmF,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGpF,QAAQ,CAAC;IAC/DqF,iBAAiB,EAAE,EAAE;IACrBC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE;EACZ,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGzF,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAAC0F,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG3F,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;EACpE,MAAM,CAAC4F,eAAe,EAAEC,kBAAkB,CAAC,GAAG7F,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAAC8F,eAAe,EAAEC,kBAAkB,CAAC,GAAG/F,QAAQ,CAAC;IACrDgG,YAAY,EAAE,MAAM;IACpBC,WAAW,EAAE,EAAE;IACfC,YAAY,EAAE,EAAE;IAChBC,aAAa,EAAE,EAAE;IACjBC,QAAQ,EAAE,SAAS;IACnBC,kBAAkB,EAAE;EACtB,CAAC,CAAC;EAEF,MAAMC,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACFxC,UAAU,CAAC,IAAI,CAAC;MAChB;MACA;MACAE,QAAQ,CAAC,IAAI,CAAC;IAChB,CAAC,CAAC,OAAOuC,GAAG,EAAE;MACZC,OAAO,CAACzC,KAAK,CAAC,uCAAuC,EAAEwC,GAAG,CAAC;MAC3DvC,QAAQ,CAAC,sCAAsC,CAAC;IAClD,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM2C,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF,MAAMC,KAAK,GAAG,MAAM1D,cAAc,CAAC2D,qBAAqB,CAACnD,UAAU,CAAC;MACpEY,cAAc,CAACsC,KAAK,CAAC;IACvB,CAAC,CAAC,OAAOH,GAAG,EAAE;MACZC,OAAO,CAACzC,KAAK,CAAC,2CAA2C,EAAEwC,GAAG,CAAC;IACjE;EACF,CAAC;EAED,MAAMK,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACFlC,sBAAsB,CAAC,IAAI,CAAC;MAC5BV,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAM6C,IAAI,GAAG,MAAM5D,mBAAmB,CAAC6D,uBAAuB,CAACtD,UAAU,CAAC;MAC1EgB,eAAe,CAACqC,IAAI,IAAI,EAAE,CAAC;MAC3B,MAAME,0BAA0B,CAACF,IAAI,IAAI,EAAE,CAAC;IAC9C,CAAC,CAAC,OAAON,GAAG,EAAE;MAAA,IAAAS,aAAA,EAAAC,kBAAA;MACZT,OAAO,CAACzC,KAAK,CAAC,0CAA0C,EAAEwC,GAAG,CAAC;MAC9D,MAAMW,YAAY,GAAG,EAAAF,aAAA,GAAAT,GAAG,CAACY,QAAQ,cAAAH,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAAcH,IAAI,cAAAI,kBAAA,uBAAlBA,kBAAA,CAAoBG,MAAM,KAAIb,GAAG,CAACc,OAAO,IAAI,yCAAyC;MAC3GrD,QAAQ,CAAC,4CAA4CkD,YAAY,EAAE,CAAC;IACtE,CAAC,SAAS;MACRxC,sBAAsB,CAAC,KAAK,CAAC;IAC/B;EACF,CAAC;;EAED;EACAzE,SAAS,CAAC,MAAM;IACd,IAAIuD,UAAU,EAAE;MACdoD,gBAAgB,CAAC,CAAC;MAClBN,WAAW,CAAC,CAAC;MACbG,eAAe,CAAC,CAAC;IACnB;EACF,CAAC,EAAE,CAACjD,UAAU,CAAC,CAAC;;EAEhB;EACAvD,SAAS,CAAC,MAAM;IACd,MAAMqH,YAAY,GAAG3D,YAAY,CAAC4D,GAAG,CAAC,SAAS,CAAC;IAChDf,OAAO,CAACgB,GAAG,CAAC,qCAAqC,EAAEF,YAAY,CAAC;IAChEd,OAAO,CAACgB,GAAG,CAAC,gBAAgB,EAAE;MAC5BjD,YAAY,EAAEA,YAAY,CAACkD,MAAM;MACjC9C,sBAAsB,EAAE+C,MAAM,CAACC,IAAI,CAAChD,sBAAsB,CAAC,CAAC8C,MAAM;MAClE5D,OAAO;MACPY;IACF,CAAC,CAAC;;IAEF;IACA,IAAI6C,YAAY,IAAIA,YAAY,KAAKrD,gBAAgB,EAAE;MACrDC,mBAAmB,CAACoD,YAAY,CAAC;IACnC;IAEA,IAAIA,YAAY,IAAI/C,YAAY,CAACkD,MAAM,GAAG,CAAC,IAAIC,MAAM,CAACC,IAAI,CAAChD,sBAAsB,CAAC,CAAC8C,MAAM,GAAG,CAAC,EAAE;MAC7FjB,OAAO,CAACgB,GAAG,CAAC,0CAA0C,CAAC;;MAEvD;MACA,IAAII,cAAc,GAAG,IAAI;MAEzB,KAAK,MAAM1B,YAAY,IAAI3B,YAAY,EAAE;QACvC,MAAMsD,WAAW,GAAGlD,sBAAsB,CAACuB,YAAY,CAAC4B,eAAe,CAAC,IAAI,EAAE;QAC9EtB,OAAO,CAACgB,GAAG,CAAC,mBAAmBtB,YAAY,CAACb,iBAAiB,KAAKwC,WAAW,CAACJ,MAAM,UAAU,CAAC;QAC/FM,cAAc,GAAGF,WAAW,CAACG,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,cAAc,KAAKZ,YAAY,CAAC;QACzE,IAAIM,cAAc,EAAE;UAClBpB,OAAO,CAACgB,GAAG,CAAC,oBAAoB,EAAEI,cAAc,CAAC;UACjD;QACF;MACF;MAEA,IAAIA,cAAc,EAAE;QAClBpB,OAAO,CAACgB,GAAG,CAAC,wCAAwC,EAAEF,YAAY,CAAC;QACnEpD,mBAAmB,CAAC,IAAI,CAAC,CAAC,CAAC;QAC3BiE,uBAAuB,CAAC,MAAM,EAAEP,cAAc,CAAC;QAC/C;QACAQ,UAAU,CAAC,MAAM;UACfxE,eAAe,CAACyE,IAAI,IAAI;YACtB,MAAMC,SAAS,GAAG,IAAIC,eAAe,CAACF,IAAI,CAAC;YAC3CC,SAAS,CAACE,MAAM,CAAC,SAAS,CAAC;YAC3B,OAAOF,SAAS;UAClB,CAAC,CAAC;QACJ,CAAC,EAAE,GAAG,CAAC;MACT,CAAC,MAAM;QACL9B,OAAO,CAACiC,IAAI,CAAC,yBAAyB,EAAEnB,YAAY,CAAC;QACrDd,OAAO,CAACgB,GAAG,CAAC,yBAAyB,CAAC;QACtCjD,YAAY,CAACmE,OAAO,CAACC,IAAI,IAAI;UAC3B,MAAMC,OAAO,GAAGjE,sBAAsB,CAACgE,IAAI,CAACb,eAAe,CAAC,IAAI,EAAE;UAClEc,OAAO,CAACF,OAAO,CAACG,GAAG,IAAI;YACrBrC,OAAO,CAACgB,GAAG,CAAC,OAAOqB,GAAG,CAACX,cAAc,KAAKS,IAAI,CAACtD,iBAAiB,GAAG,CAAC;UACtE,CAAC,CAAC;QACJ,CAAC,CAAC;;QAEF;QACA;QACA,IAAI,CAACxB,OAAO,IAAI,CAACY,mBAAmB,EAAE;UACpC+B,OAAO,CAACgB,GAAG,CAAC,uCAAuC,CAAC;UACpDY,UAAU,CAAC,MAAM;YACfxB,gBAAgB,CAAC,CAAC;UACpB,CAAC,EAAE,GAAG,CAAC;QACT;MACF;IACF,CAAC,MAAM,IAAIU,YAAY,EAAE;MACvBd,OAAO,CAACgB,GAAG,CAAC,wCAAwC,CAAC;;MAErD;MACA,IAAI,CAAC3D,OAAO,IAAI,CAACY,mBAAmB,IAAIF,YAAY,CAACkD,MAAM,KAAK,CAAC,EAAE;QACjEjB,OAAO,CAACgB,GAAG,CAAC,0CAA0C,CAAC;QACvDZ,gBAAgB,CAAC,CAAC;MACpB;IACF;EACF,CAAC,EAAE,CAACjD,YAAY,EAAEY,YAAY,EAAEI,sBAAsB,EAAEd,OAAO,EAAEY,mBAAmB,CAAC,CAAC;EAEtF,MAAMsC,0BAA0B,GAAG,MAAO+B,gBAAgB,IAAK;IAC7D,IAAI;MACF,MAAMC,UAAU,GAAG,CAAC,CAAC;MACrB,KAAK,MAAM7C,YAAY,IAAI4C,gBAAgB,EAAE;QAC3C,IAAI;UACF,MAAM3B,QAAQ,GAAG,MAAMnE,cAAc,CAACgG,wBAAwB,CAACxF,UAAU,EAAE0C,YAAY,CAACb,iBAAiB,CAAC;UAC1G;UACA,IAAIuD,OAAO,GAAG,EAAE;UAChB,IAAIzB,QAAQ,IAAI8B,KAAK,CAACC,OAAO,CAAC/B,QAAQ,CAAC,EAAE;YACvCyB,OAAO,GAAGzB,QAAQ;UACpB,CAAC,MAAM,IAAIA,QAAQ,IAAIA,QAAQ,CAACyB,OAAO,IAAIK,KAAK,CAACC,OAAO,CAAC/B,QAAQ,CAACyB,OAAO,CAAC,EAAE;YAC1EA,OAAO,GAAGzB,QAAQ,CAACyB,OAAO;UAC5B,CAAC,MAAM,IAAIzB,QAAQ,IAAIA,QAAQ,CAACN,IAAI,IAAIoC,KAAK,CAACC,OAAO,CAAC/B,QAAQ,CAACN,IAAI,CAAC,EAAE;YACpE+B,OAAO,GAAGzB,QAAQ,CAACN,IAAI;UACzB;UACAkC,UAAU,CAAC7C,YAAY,CAAC4B,eAAe,CAAC,GAAGc,OAAO;QACpD,CAAC,CAAC,OAAOrC,GAAG,EAAE;UACZC,OAAO,CAACzC,KAAK,CAAC,sCAAsCmC,YAAY,CAACb,iBAAiB,GAAG,EAAEkB,GAAG,CAAC;UAC3FwC,UAAU,CAAC7C,YAAY,CAAC4B,eAAe,CAAC,GAAG,EAAE;QAC/C;MACF;MACAlD,yBAAyB,CAACmE,UAAU,CAAC;IACvC,CAAC,CAAC,OAAOxC,GAAG,EAAE;MACZC,OAAO,CAACzC,KAAK,CAAC,uCAAuC,EAAEwC,GAAG,CAAC;IAC7D;EACF,CAAC;;EAED;EACA,MAAM4C,4BAA4B,GAAGA,CAACC,IAAI,EAAElD,YAAY,GAAG,IAAI,KAAK;IAClElB,yBAAyB,CAACoE,IAAI,CAAC;IAC/BlE,uBAAuB,CAACgB,YAAY,CAAC;IAErC,IAAIkD,IAAI,KAAK,MAAM,IAAIlD,YAAY,EAAE;MACnCd,uBAAuB,CAAC;QACtBC,iBAAiB,EAAEa,YAAY,CAACb,iBAAiB,IAAI,EAAE;QACvDC,KAAK,EAAEY,YAAY,CAACZ,KAAK,IAAI,EAAE;QAC/BC,QAAQ,EAAEW,YAAY,CAACX,QAAQ,IAAI;MACrC,CAAC,CAAC;IACJ,CAAC,MAAM;MACLH,uBAAuB,CAAC;QACtBC,iBAAiB,EAAE,EAAE;QACrBC,KAAK,EAAE,EAAE;QACTC,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ;IAEAT,yBAAyB,CAAC,IAAI,CAAC;EACjC,CAAC;EAED,MAAMuE,6BAA6B,GAAGA,CAAA,KAAM;IAC1CvE,yBAAyB,CAAC,KAAK,CAAC;IAChCI,uBAAuB,CAAC,IAAI,CAAC;IAC7BlB,QAAQ,CAAC,IAAI,CAAC;EAChB,CAAC;EAED,MAAMsF,wBAAwB,GAAG,MAAAA,CAAA,KAAY;IAC3C,IAAI;MACFtF,QAAQ,CAAC,IAAI,CAAC;MAEd,IAAI,CAACmB,oBAAoB,CAACE,iBAAiB,CAACkE,IAAI,CAAC,CAAC,EAAE;QAClDvF,QAAQ,CAAC,yCAAyC,CAAC;QACnD;MACF;MAEA,IAAI,CAACmB,oBAAoB,CAACG,KAAK,IAAI,CAACH,oBAAoB,CAACI,QAAQ,EAAE;QACjEvB,QAAQ,CAAC,yDAAyD,CAAC;QACnE;MACF;MAEA,IAAIe,sBAAsB,KAAK,QAAQ,EAAE;QACvC,MAAM9B,mBAAmB,CAACuG,kBAAkB,CAAChG,UAAU,EAAE2B,oBAAoB,CAAC;MAChF,CAAC,MAAM,IAAIJ,sBAAsB,KAAK,MAAM,EAAE;QAC5C,MAAM9B,mBAAmB,CAACwG,kBAAkB,CAACxE,oBAAoB,CAAC6C,eAAe,EAAE3C,oBAAoB,CAAC;MAC1G;MAEAkE,6BAA6B,CAAC,CAAC;MAC/B,MAAMzC,gBAAgB,CAAC,CAAC;IAC1B,CAAC,CAAC,OAAOL,GAAG,EAAE;MACZC,OAAO,CAACzC,KAAK,CAAC,yBAAyB,EAAEwC,GAAG,CAAC;MAC7CvC,QAAQ,CAACuC,GAAG,CAACa,MAAM,IAAI,yCAAyC,CAAC;IACnE;EACF,CAAC;EAED,MAAMsC,wBAAwB,GAAG,MAAOC,cAAc,IAAK;IACzD,IAAI,CAACC,MAAM,CAACC,OAAO,CAAC,oDAAoD,CAAC,EAAE;MACzE;IACF;IAEA,IAAI;MACF,MAAM5G,mBAAmB,CAAC6G,kBAAkB,CAACH,cAAc,CAAC;MAC5D,MAAM/C,gBAAgB,CAAC,CAAC;IAC1B,CAAC,CAAC,OAAOL,GAAG,EAAE;MACZC,OAAO,CAACzC,KAAK,CAAC,4BAA4B,EAAEwC,GAAG,CAAC;MAChDvC,QAAQ,CAAC,4CAA4C,CAAC;IACxD;EACF,CAAC;;EAED;EACA,MAAMmE,uBAAuB,GAAGA,CAACiB,IAAI,EAAEW,OAAO,GAAG,IAAI,KAAK;IACxDpE,oBAAoB,CAACyD,IAAI,CAAC;IAC1BvD,kBAAkB,CAACkE,OAAO,CAAC;IAE3B,IAAIX,IAAI,KAAK,MAAM,IAAIW,OAAO,EAAE;MAC9BhE,kBAAkB,CAAC;QACjBC,YAAY,EAAE+D,OAAO,CAAC/D,YAAY;QAClCC,WAAW,EAAE8D,OAAO,CAAC9D,WAAW,IAAI,EAAE;QACtCC,YAAY,EAAE6D,OAAO,CAAC7D,YAAY,IAAI,EAAE;QACxCC,aAAa,EAAE4D,OAAO,CAAC5D,aAAa,IAAI,EAAE;QAC1CC,QAAQ,EAAE2D,OAAO,CAAC3D,QAAQ,IAAI,SAAS;QACvCC,kBAAkB,EAAE0D,OAAO,CAAC1D,kBAAkB,IAAI;MACpD,CAAC,CAAC;IACJ;IAEAZ,oBAAoB,CAAC,IAAI,CAAC;EAC5B,CAAC;EAED,MAAMuE,wBAAwB,GAAGA,CAAA,KAAM;IACrCvE,oBAAoB,CAAC,KAAK,CAAC;IAC3BI,kBAAkB,CAAC,IAAI,CAAC;IACxBE,kBAAkB,CAAC;MACjBC,YAAY,EAAE,MAAM;MACpBC,WAAW,EAAE,EAAE;MACfC,YAAY,EAAE,EAAE;MAChBC,aAAa,EAAE,EAAE;MACjBC,QAAQ,EAAE,SAAS;MACnBC,kBAAkB,EAAE;IACtB,CAAC,CAAC;EACJ,CAAC;EAED,MAAM4D,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF,IAAIvE,iBAAiB,KAAK,MAAM,EAAE;QAChC,MAAM1C,cAAc,CAACkH,aAAa,CAACtE,eAAe,CAACsC,cAAc,EAAEpC,eAAe,CAAC;QACnFkE,wBAAwB,CAAC,CAAC;QAC1B,MAAMpD,gBAAgB,CAAC,CAAC,CAAC,CAAC;QAC1B,MAAMH,eAAe,CAAC,CAAC;MACzB;IACF,CAAC,CAAC,OAAOF,GAAG,EAAE;MACZC,OAAO,CAACzC,KAAK,CAAC,yBAAyB,EAAEwC,GAAG,CAAC;MAC7CvC,QAAQ,CAAC,sCAAsC,CAAC;IAClD;EACF,CAAC;EAED,MAAMmG,mBAAmB,GAAG,MAAOC,aAAa,IAAK;IACnD,IAAI,CAACR,MAAM,CAACC,OAAO,CAAC,+CAA+C,CAAC,EAAE;MACpE;IACF;IAEA,IAAI;MACF,MAAM7G,cAAc,CAACqH,aAAa,CAACD,aAAa,CAAC;MACjD,MAAMxD,gBAAgB,CAAC,CAAC,CAAC,CAAC;MAC1B,MAAMH,eAAe,CAAC,CAAC;IACzB,CAAC,CAAC,OAAOF,GAAG,EAAE;MACZC,OAAO,CAACzC,KAAK,CAAC,4BAA4B,EAAEwC,GAAG,CAAC;MAChDvC,QAAQ,CAAC,yCAAyC,CAAC;IACrD;EACF,CAAC;EAED,MAAMsG,mBAAmB,GAAIC,IAAI,IAAK;IACpC,MAAMC,MAAM,GAAG;MACb,MAAM,EAAE,MAAM;MACd,uBAAuB,EAAE,gBAAgB;MACzC,qBAAqB,EAAE,cAAc;MACrC,gBAAgB,EAAE,gBAAgB;MAClC,SAAS,EAAE;IACb,CAAC;IACD,OAAOA,MAAM,CAACD,IAAI,CAAC,IAAIA,IAAI;EAC7B,CAAC;EAED,MAAME,aAAa,GAAIC,KAAK,IAAK;IAC/B,MAAMC,MAAM,GAAG;MACb,QAAQ,EAAE,SAAS;MACnB,WAAW,EAAE,SAAS;MACtB,UAAU,EAAE,SAAS;MACrB,YAAY,EAAE,SAAS;MACvB,WAAW,EAAE;IACf,CAAC;IACD,OAAOA,MAAM,CAACD,KAAK,CAAC,IAAI,SAAS;EACnC,CAAC;EAED,MAAME,gBAAgB,GAAIxE,QAAQ,IAAK;IACrC,MAAMuE,MAAM,GAAG;MACb,OAAO,EAAE,SAAS;MAClB,SAAS,EAAE,SAAS;MACpB,MAAM,EAAE,SAAS;MACjB,SAAS,EAAE;IACb,CAAC;IACD,OAAOA,MAAM,CAACvE,QAAQ,CAAC,IAAI,SAAS;EACtC,CAAC;EAED,IAAIvC,OAAO,EAAE;IACX,oBACET,OAAA,CAACjD,GAAG;MAAC0K,OAAO,EAAC,MAAM;MAACC,cAAc,EAAC,QAAQ;MAACC,UAAU,EAAC,QAAQ;MAACC,SAAS,EAAC,OAAO;MAAAC,QAAA,eAC/E7H,OAAA,CAACrC,gBAAgB;QAAAmK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CAAC;EAEV;EAEA,oBACEjI,OAAA,CAACjD,GAAG;IAACmL,EAAE,EAAE;MAAEC,CAAC,EAAE;IAAE,CAAE;IAAAN,QAAA,gBAEhB7H,OAAA,CAACjD,GAAG;MAACqL,EAAE,EAAE,CAAE;MAAAP,QAAA,eACT7H,OAAA,CAAChD,UAAU;QAACqL,OAAO,EAAC,IAAI;QAACH,EAAE,EAAE;UAAEI,UAAU,EAAE,GAAG;UAAEC,KAAK,EAAE;QAAe,CAAE;QAAAV,QAAA,EACrExH;MAAY;QAAAyH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,EAELtH,KAAK,iBACJX,OAAA,CAACtC,KAAK;MAAC8K,QAAQ,EAAC,OAAO;MAACN,EAAE,EAAE;QAAEE,EAAE,EAAE;MAAE,CAAE;MAAAP,QAAA,EACnClH;IAAK;MAAAmH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR,EAEApH,gBAAgB,iBACfb,OAAA,CAACtC,KAAK;MAAC8K,QAAQ,EAAC,MAAM;MAACN,EAAE,EAAE;QAAEE,EAAE,EAAE;MAAE,CAAE;MAAAP,QAAA,GAAC,+BACjB,EAAChH,gBAAgB,EAAC,cACvC;IAAA;MAAAiH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CACR,EAGAlH,WAAW,iBACVf,OAAA,CAAC9C,KAAK;MAACgL,EAAE,EAAE;QAAEC,CAAC,EAAE,CAAC;QAAEC,EAAE,EAAE,CAAC;QAAEK,OAAO,EAAE;MAAU,CAAE;MAAAZ,QAAA,eAC7C7H,OAAA,CAAC7B,KAAK;QAACuK,SAAS,EAAC,KAAK;QAACC,OAAO,EAAE,CAAE;QAAChB,UAAU,EAAC,QAAQ;QAACD,cAAc,EAAC,eAAe;QAACkB,QAAQ,EAAC,MAAM;QAAAf,QAAA,gBAEnG7H,OAAA,CAAC7B,KAAK;UAACuK,SAAS,EAAC,KAAK;UAACf,UAAU,EAAC,QAAQ;UAACgB,OAAO,EAAE,CAAE;UAAAd,QAAA,gBACpD7H,OAAA,CAACjB,UAAU;YAACwJ,KAAK,EAAC,SAAS;YAACM,QAAQ,EAAC;UAAO;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/CjI,OAAA,CAACjD,GAAG;YAAA8K,QAAA,gBACF7H,OAAA,CAAChD,UAAU;cAACqL,OAAO,EAAC,IAAI;cAACC,UAAU,EAAC,MAAM;cAACJ,EAAE,EAAE;gBAAEY,UAAU,EAAE;cAAE,CAAE;cAAAjB,QAAA,EAC9D9G,WAAW,CAACgI,mBAAmB,IAAI;YAAC;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC,eACbjI,OAAA,CAAChD,UAAU;cAACqL,OAAO,EAAC,SAAS;cAACE,KAAK,EAAC,gBAAgB;cAAAV,QAAA,EAAC;YAErD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAGRjI,OAAA,CAAC7B,KAAK;UAACuK,SAAS,EAAC,KAAK;UAACf,UAAU,EAAC,QAAQ;UAACgB,OAAO,EAAE,CAAE;UAAAd,QAAA,gBACpD7H,OAAA,CAACnB,UAAU;YAAC0J,KAAK,EAAC,MAAM;YAACM,QAAQ,EAAC;UAAO;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5CjI,OAAA,CAACjD,GAAG;YAAA8K,QAAA,gBACF7H,OAAA,CAAChD,UAAU;cAACqL,OAAO,EAAC,IAAI;cAACC,UAAU,EAAC,MAAM;cAACJ,EAAE,EAAE;gBAAEY,UAAU,EAAE;cAAE,CAAE;cAAAjB,QAAA,EAC9D9G,WAAW,CAACiI,cAAc,IAAI;YAAC;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC,eACbjI,OAAA,CAAChD,UAAU;cAACqL,OAAO,EAAC,SAAS;cAACE,KAAK,EAAC,gBAAgB;cAAAV,QAAA,EAAC;YAErD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAGRjI,OAAA,CAAC7B,KAAK;UAACuK,SAAS,EAAC,KAAK;UAACf,UAAU,EAAC,QAAQ;UAACgB,OAAO,EAAE,CAAE;UAAAd,QAAA,gBACpD7H,OAAA,CAACT,eAAe;YAACgJ,KAAK,EAAC,SAAS;YAACM,QAAQ,EAAC;UAAO;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACpDjI,OAAA,CAACjD,GAAG;YAAA8K,QAAA,gBACF7H,OAAA,CAAChD,UAAU;cAACqL,OAAO,EAAC,IAAI;cAACC,UAAU,EAAC,MAAM;cAACJ,EAAE,EAAE;gBAAEY,UAAU,EAAE;cAAE,CAAE;cAAAjB,QAAA,EAC9D9G,WAAW,CAACkI,gBAAgB,IAAI;YAAC;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC,eACbjI,OAAA,CAAChD,UAAU;cAACqL,OAAO,EAAC,SAAS;cAACE,KAAK,EAAC,gBAAgB;cAAAV,QAAA,EAAC;YAErD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAGRjI,OAAA,CAAC7B,KAAK;UAACuK,SAAS,EAAC,KAAK;UAACf,UAAU,EAAC,QAAQ;UAACgB,OAAO,EAAE,CAAE;UAAAd,QAAA,gBACpD7H,OAAA,CAACP,YAAY;YAAC8I,KAAK,EAAC,SAAS;YAACM,QAAQ,EAAC;UAAO;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjDjI,OAAA,CAACjD,GAAG;YAAA8K,QAAA,gBACF7H,OAAA,CAAChD,UAAU;cAACqL,OAAO,EAAC,IAAI;cAACC,UAAU,EAAC,MAAM;cAACJ,EAAE,EAAE;gBAAEY,UAAU,EAAE;cAAE,CAAE;cAAAjB,QAAA,EAC9D9G,WAAW,CAACmI,kBAAkB,IAAI;YAAC;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CAAC,eACbjI,OAAA,CAAChD,UAAU;cAACqL,OAAO,EAAC,SAAS;cAACE,KAAK,EAAC,gBAAgB;cAAAV,QAAA,EAAC;YAErD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAGRjI,OAAA,CAAC7B,KAAK;UAACuK,SAAS,EAAC,KAAK;UAACf,UAAU,EAAC,QAAQ;UAACgB,OAAO,EAAE,CAAE;UAAAd,QAAA,gBACpD7H,OAAA,CAACjD,GAAG;YAACmL,EAAE,EAAE;cACPiB,KAAK,EAAE,EAAE;cACTC,MAAM,EAAE,EAAE;cACVC,YAAY,EAAE,KAAK;cACnBZ,OAAO,EAAG1H,WAAW,CAACmI,kBAAkB,IAAInI,WAAW,CAACiI,cAAc,IAAI,CAAC,CAAC,IAAK,GAAG,GAAG,cAAc,GAC3FjI,WAAW,CAACmI,kBAAkB,IAAInI,WAAW,CAACiI,cAAc,IAAI,CAAC,CAAC,IAAK,GAAG,GAAG,cAAc,GAAG,YAAY;cACpHvB,OAAO,EAAE,MAAM;cACfE,UAAU,EAAE,QAAQ;cACpBD,cAAc,EAAE;YAClB,CAAE;YAAAG,QAAA,eACA7H,OAAA,CAAChD,UAAU;cAACqL,OAAO,EAAC,SAAS;cAACC,UAAU,EAAC,MAAM;cAACC,KAAK,EAAC,OAAO;cAAAV,QAAA,GAC1D9G,WAAW,CAACiI,cAAc,GAAG,CAAC,GAAGM,IAAI,CAACC,KAAK,CAAExI,WAAW,CAACmI,kBAAkB,GAAGnI,WAAW,CAACiI,cAAc,GAAI,GAAG,CAAC,GAAG,CAAC,EAAC,GACxH;YAAA;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACNjI,OAAA,CAACjD,GAAG;YAAA8K,QAAA,gBACF7H,OAAA,CAAChD,UAAU;cAACqL,OAAO,EAAC,OAAO;cAACC,UAAU,EAAC,QAAQ;cAACJ,EAAE,EAAE;gBAAEY,UAAU,EAAE;cAAE,CAAE;cAAAjB,QAAA,EAAC;YAEvE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbjI,OAAA,CAAChD,UAAU;cAACqL,OAAO,EAAC,SAAS;cAACE,KAAK,EAAC,gBAAgB;cAAAV,QAAA,GACjD9G,WAAW,CAACyI,cAAc,IAAI,CAAC,EAAC,SACnC;YAAA;cAAA1B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACR,eAGDjI,OAAA,CAACjD,GAAG;MAAA8K,QAAA,eACF7H,OAAA,CAACjD,GAAG;QAAA8K,QAAA,gBAEF7H,OAAA,CAACjD,GAAG;UAAC0K,OAAO,EAAC,MAAM;UAACC,cAAc,EAAC,eAAe;UAACC,UAAU,EAAC,QAAQ;UAACS,EAAE,EAAE,CAAE;UAAAP,QAAA,gBAC3E7H,OAAA,CAAChD,UAAU;YAACqL,OAAO,EAAC,IAAI;YAACH,EAAE,EAAE;cAAEI,UAAU,EAAE,GAAG;cAAEC,KAAK,EAAE;YAAe,CAAE;YAAAV,QAAA,EAAC;UAEzE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbjI,OAAA,CAAC/C,MAAM;YACLoL,OAAO,EAAC,WAAW;YACnBoB,SAAS,eAAEzJ,OAAA,CAACzB,OAAO;cAAAuJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvByB,OAAO,EAAEA,CAAA,KAAM3D,4BAA4B,CAAC,QAAQ,CAAE;YACtDmC,EAAE,EAAE;cACFyB,aAAa,EAAE,MAAM;cACrBrB,UAAU,EAAE,GAAG;cACfsB,EAAE,EAAE,CAAC;cACLC,EAAE,EAAE,CAAC;cACLC,eAAe,EAAE,SAAS;cAC1BvB,KAAK,EAAE,SAAS;cAChBwB,MAAM,EAAE,mBAAmB;cAC3BC,SAAS,EAAE,MAAM;cACjB,SAAS,EAAE;gBACTF,eAAe,EAAE,SAAS;gBAC1BE,SAAS,EAAE;cACb;YACF,CAAE;YAAAnC,QAAA,EACH;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EAEL5G,mBAAmB,gBAClBrB,OAAA,CAACjD,GAAG;UAAC0K,OAAO,EAAC,MAAM;UAACC,cAAc,EAAC,QAAQ;UAACmC,EAAE,EAAE,CAAE;UAAAhC,QAAA,eAChD7H,OAAA,CAACrC,gBAAgB;YAAAmK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,gBAENjI,OAAA,CAACjD,GAAG;UAAA8K,QAAA,EACD1G,YAAY,CAACkD,MAAM,KAAK,CAAC,gBACxBrE,OAAA,CAAC9C,KAAK;YACJ+M,SAAS,EAAE,CAAE;YACb/B,EAAE,EAAE;cACFC,CAAC,EAAE,CAAC;cACJ+B,SAAS,EAAE,QAAQ;cACnBJ,eAAe,EAAE,SAAS;cAC1BC,MAAM,EAAE,YAAY;cACpBI,WAAW,EAAE;YACf,CAAE;YAAAtC,QAAA,gBAEF7H,OAAA,CAACjB,UAAU;cAACmJ,EAAE,EAAE;gBAAEW,QAAQ,EAAE,EAAE;gBAAEN,KAAK,EAAE,UAAU;gBAAEH,EAAE,EAAE;cAAE;YAAE;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9DjI,OAAA,CAAChD,UAAU;cAACqL,OAAO,EAAC,IAAI;cAACE,KAAK,EAAC,gBAAgB;cAAC6B,YAAY;cAAAvC,QAAA,EAAC;YAE7D;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbjI,OAAA,CAAChD,UAAU;cAACqL,OAAO,EAAC,OAAO;cAACE,KAAK,EAAC,gBAAgB;cAACL,EAAE,EAAE;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAP,QAAA,EAAC;YAElE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbjI,OAAA,CAAC/C,MAAM;cACLoL,OAAO,EAAC,WAAW;cACnBoB,SAAS,eAAEzJ,OAAA,CAACzB,OAAO;gBAAAuJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACvByB,OAAO,EAAEA,CAAA,KAAM3D,4BAA4B,CAAC,QAAQ,CAAE;cACtDmC,EAAE,EAAE;gBACFyB,aAAa,EAAE,MAAM;gBACrBG,eAAe,EAAE,SAAS;gBAC1BvB,KAAK,EAAE,SAAS;gBAChBwB,MAAM,EAAE,mBAAmB;gBAC3BC,SAAS,EAAE,MAAM;gBACjB,SAAS,EAAE;kBACTF,eAAe,EAAE,SAAS;kBAC1BE,SAAS,EAAE;gBACb;cACF,CAAE;cAAAnC,QAAA,EACH;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,GAER9G,YAAY,CAACkJ,GAAG,CAAEvH,YAAY,iBAC5B9C,OAAA,CAAChC,SAAS;YAERkK,EAAE,EAAE;cACFE,EAAE,EAAE,CAAC;cACL,UAAU,EAAE;gBAAEX,OAAO,EAAE;cAAO,CAAC;cAC/BuC,SAAS,EAAE,2BAA2B;cACtCD,MAAM,EAAE,WAAW;cACnBI,WAAW,EAAE;YACf,CAAE;YAAAtC,QAAA,gBAEF7H,OAAA,CAAC/B,gBAAgB;cACfqM,UAAU,eAAEtK,OAAA,CAACX,cAAc;gBAAAyI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC/BC,EAAE,EAAE;gBACF,SAAS,EAAE;kBACT4B,eAAe,EAAE;gBACnB;cACF,CAAE;cAAAjC,QAAA,eAEF7H,OAAA,CAACjD,GAAG;gBAAC0K,OAAO,EAAC,MAAM;gBAACE,UAAU,EAAC,QAAQ;gBAACD,cAAc,EAAC,eAAe;gBAACyB,KAAK,EAAC,MAAM;gBAAAtB,QAAA,gBACjF7H,OAAA,CAACjD,GAAG;kBAAC0K,OAAO,EAAC,MAAM;kBAACE,UAAU,EAAC,QAAQ;kBAAC4C,GAAG,EAAE,CAAE;kBAAA1C,QAAA,gBAC7C7H,OAAA,CAACjB,UAAU;oBAACwJ,KAAK,EAAC,SAAS;oBAACL,EAAE,EAAE;sBAAEW,QAAQ,EAAE;oBAAG;kBAAE;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACpDjI,OAAA,CAACjD,GAAG;oBAAA8K,QAAA,gBACF7H,OAAA,CAAChD,UAAU;sBAACqL,OAAO,EAAC,IAAI;sBAACH,EAAE,EAAE;wBAAEI,UAAU,EAAE;sBAAI,CAAE;sBAAAT,QAAA,EAC9C/E,YAAY,CAACb;oBAAiB;sBAAA6F,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrB,CAAC,eACbjI,OAAA,CAACjD,GAAG;sBAAC0K,OAAO,EAAC,MAAM;sBAAC8C,GAAG,EAAE,CAAE;sBAACC,EAAE,EAAE,GAAI;sBAAA3C,QAAA,GACjC/E,YAAY,CAACZ,KAAK,iBACjBlC,OAAA,CAACjD,GAAG;wBAAC0K,OAAO,EAAC,MAAM;wBAACE,UAAU,EAAC,QAAQ;wBAAC4C,GAAG,EAAE,GAAI;wBAAA1C,QAAA,gBAC/C7H,OAAA,CAACf,SAAS;0BAAC4J,QAAQ,EAAC,OAAO;0BAACN,KAAK,EAAC;wBAAQ;0BAAAT,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAC7CjI,OAAA,CAAChD,UAAU;0BAACqL,OAAO,EAAC,OAAO;0BAACE,KAAK,EAAC,gBAAgB;0BAAAV,QAAA,EAC/C/E,YAAY,CAACZ;wBAAK;0BAAA4F,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACT,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACV,CACN,EACAnF,YAAY,CAACX,QAAQ,iBACpBnC,OAAA,CAACjD,GAAG;wBAAC0K,OAAO,EAAC,MAAM;wBAACE,UAAU,EAAC,QAAQ;wBAAC4C,GAAG,EAAE,GAAI;wBAAA1C,QAAA,gBAC/C7H,OAAA,CAACb,SAAS;0BAAC0J,QAAQ,EAAC,OAAO;0BAACN,KAAK,EAAC;wBAAQ;0BAAAT,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAC7CjI,OAAA,CAAChD,UAAU;0BAACqL,OAAO,EAAC,OAAO;0BAACE,KAAK,EAAC,gBAAgB;0BAAAV,QAAA,EAC/C/E,YAAY,CAACX;wBAAQ;0BAAA2F,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACZ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACV,CACN;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENjI,OAAA,CAACjD,GAAG;kBAAC0K,OAAO,EAAC,MAAM;kBAACE,UAAU,EAAC,QAAQ;kBAAC4C,GAAG,EAAE,CAAE;kBAACb,OAAO,EAAGe,CAAC,IAAKA,CAAC,CAACC,eAAe,CAAC,CAAE;kBAAA7C,QAAA,gBAClF7H,OAAA,CAAC7C,IAAI;oBACHwN,IAAI,eAAE3K,OAAA,CAACnB,UAAU;sBAAAiJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAE;oBACrB2C,KAAK,EAAE,GAAG/E,KAAK,CAACC,OAAO,CAACvE,sBAAsB,CAACuB,YAAY,CAAC4B,eAAe,CAAC,CAAC,GAAGnD,sBAAsB,CAACuB,YAAY,CAAC4B,eAAe,CAAC,CAACL,MAAM,GAAG,CAAC,UAAW;oBAC1JwG,IAAI,EAAC,OAAO;oBACZtC,KAAK,EAAC,SAAS;oBACfF,OAAO,EAAC,UAAU;oBAClByC,SAAS;oBACTpB,OAAO,EAAEA,CAAA,KAAM;sBACb;sBACAxI,kBAAkB,CAAC,IAAI,CAAC;oBAC1B,CAAE;oBACFgH,EAAE,EAAE;sBACFI,UAAU,EAAE,GAAG;sBACfwB,eAAe,EAAE,yBAAyB;sBAC1CvB,KAAK,EAAE,SAAS;sBAChBwB,MAAM,EAAE,mCAAmC;sBAC3C,SAAS,EAAE;wBACTD,eAAe,EAAE,oCAAoC;wBACrDvB,KAAK,EAAE;sBACT;oBACF;kBAAE;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACFjI,OAAA,CAACpC,OAAO;oBAACmN,KAAK,EAAC,uBAAuB;oBAAAlD,QAAA,eACpC7H,OAAA,CAAC5C,UAAU;sBACTyN,IAAI,EAAC,OAAO;sBACZnB,OAAO,EAAEA,CAAA,KAAM3D,4BAA4B,CAAC,MAAM,EAAEjD,YAAY,CAAE;sBAClEoF,EAAE,EAAE;wBACFK,KAAK,EAAE,SAAS;wBAChB,SAAS,EAAE;0BACTuB,eAAe,EAAE;wBACnB;sBACF,CAAE;sBAAAjC,QAAA,eAEF7H,OAAA,CAACvB,QAAQ;wBAACoK,QAAQ,EAAC;sBAAO;wBAAAf,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACVjI,OAAA,CAACpC,OAAO;oBAACmN,KAAK,EAAC,sBAAsB;oBAAAlD,QAAA,eACnC7H,OAAA,CAAC5C,UAAU;sBACTyN,IAAI,EAAC,OAAO;sBACZnB,OAAO,EAAEA,CAAA,KAAMpD,wBAAwB,CAACxD,YAAY,CAAC4B,eAAe,CAAE;sBACtEwD,EAAE,EAAE;wBACFK,KAAK,EAAE,SAAS;wBAChB,SAAS,EAAE;0BACTuB,eAAe,EAAE;wBACnB;sBACF,CAAE;sBAAAjC,QAAA,eAEF7H,OAAA,CAACrB,UAAU;wBAACkK,QAAQ,EAAC;sBAAO;wBAAAf,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACU,CAAC,eAEnBjI,OAAA,CAAC9B,gBAAgB;cAACgK,EAAE,EAAE;gBAAE8C,EAAE,EAAE;cAAE,CAAE;cAAAnD,QAAA,gBAC9B7H,OAAA,CAAChD,UAAU;gBAACqL,OAAO,EAAC,WAAW;gBAAC+B,YAAY;gBAAClC,EAAE,EAAE;kBAAEI,UAAU,EAAE,GAAG;kBAAEF,EAAE,EAAE;gBAAE,CAAE;gBAAAP,QAAA,EAAC;cAE7E;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,EAEX,CAACpC,KAAK,CAACC,OAAO,CAACvE,sBAAsB,CAACuB,YAAY,CAAC4B,eAAe,CAAC,CAAC,IAAInD,sBAAsB,CAACuB,YAAY,CAAC4B,eAAe,CAAC,CAACL,MAAM,KAAK,CAAC,gBACzIrE,OAAA,CAACjD,GAAG;gBACFmL,EAAE,EAAE;kBACFC,CAAC,EAAE,CAAC;kBACJ+B,SAAS,EAAE,QAAQ;kBACnBJ,eAAe,EAAE,SAAS;kBAC1BT,YAAY,EAAE,CAAC;kBACfU,MAAM,EAAE,YAAY;kBACpBI,WAAW,EAAE;gBACf,CAAE;gBAAAtC,QAAA,eAEF7H,OAAA,CAAChD,UAAU;kBAACqL,OAAO,EAAC,OAAO;kBAACE,KAAK,EAAC,gBAAgB;kBAAAV,QAAA,EAAC;gBAEnD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,gBAENjI,OAAA,CAACnC,IAAI;gBAACoN,KAAK;gBAAApD,QAAA,EACRhC,KAAK,CAACC,OAAO,CAACvE,sBAAsB,CAACuB,YAAY,CAAC4B,eAAe,CAAC,CAAC,IAAInD,sBAAsB,CAACuB,YAAY,CAAC4B,eAAe,CAAC,CAAC2F,GAAG,CAAE1D,OAAO,iBACvI3G,OAAA,CAAClC,QAAQ;kBAEPoN,OAAO;kBACPhD,EAAE,EAAE;oBACF,SAAS,EAAE;sBACT4B,eAAe,EAAE;oBACnB;kBACF,CAAE;kBAAAjC,QAAA,gBAEF7H,OAAA,CAACjC,YAAY;oBACXoN,OAAO,eACLnL,OAAA,CAACjD,GAAG;sBAAC0K,OAAO,EAAC,MAAM;sBAACE,UAAU,EAAC,QAAQ;sBAAC4C,GAAG,EAAE,CAAE;sBAAA1C,QAAA,gBAC7C7H,OAAA,CAAChD,UAAU;wBAACqL,OAAO,EAAC,OAAO;wBAACC,UAAU,EAAC,MAAM;wBAAAT,QAAA,EAC1ClB,OAAO,CAAC7B;sBAAc;wBAAAgD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACb,CAAC,eACbjI,OAAA,CAAC7C,IAAI;wBACHyN,KAAK,EAAE1D,mBAAmB,CAACP,OAAO,CAAC/D,YAAY,CAAE;wBACjDiI,IAAI,EAAC,OAAO;wBACZxC,OAAO,EAAC;sBAAU;wBAAAP,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnB,CAAC,eACFjI,OAAA,CAAC7C,IAAI;wBACHyN,KAAK,EAAEjE,OAAO,CAACW,KAAK,IAAI,QAAS;wBACjCuD,IAAI,EAAC,OAAO;wBACZtC,KAAK,EAAElB,aAAa,CAACV,OAAO,CAACW,KAAK;sBAAE;wBAAAQ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrC,CAAC,EACDtB,OAAO,CAAC3D,QAAQ,IAAI2D,OAAO,CAAC3D,QAAQ,KAAK,SAAS,iBACjDhD,OAAA,CAAC7C,IAAI;wBACHyN,KAAK,EAAEjE,OAAO,CAAC3D,QAAS;wBACxB6H,IAAI,EAAC,OAAO;wBACZtC,KAAK,EAAEf,gBAAgB,CAACb,OAAO,CAAC3D,QAAQ,CAAE;wBAC1CqF,OAAO,EAAC;sBAAQ;wBAAAP,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjB,CACF;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CACN;oBACDmD,SAAS,eACPpL,OAAA,CAACjD,GAAG;sBAAA8K,QAAA,gBACF7H,OAAA,CAAChD,UAAU;wBAACqL,OAAO,EAAC,OAAO;wBAACE,KAAK,EAAC,eAAe;wBAAAV,QAAA,GAC9ClB,OAAO,CAAC9D,WAAW,IAAI,qBAAqB,EAC5C8D,OAAO,CAAC0E,cAAc,IAAI,cAAc,IAAIC,IAAI,CAAC3E,OAAO,CAAC0E,cAAc,CAAC,CAACE,kBAAkB,CAAC,CAAC,EAAE;sBAAA;wBAAAzD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtF,CAAC,EACZtB,OAAO,CAAC6E,qBAAqB,GAAG,CAAC,iBAChCxL,OAAA,CAAChD,UAAU;wBAACqL,OAAO,EAAC,SAAS;wBAACE,KAAK,EAAC,SAAS;wBAAAV,QAAA,GAC1ClB,OAAO,CAAC6E,qBAAqB,EAAC,iBAC/B,EAAC7E,OAAO,CAAC8E,yBAAyB,IAAI,MAAM9E,OAAO,CAAC8E,yBAAyB,CAACC,OAAO,CAAC,CAAC,CAAC,cAAc;sBAAA;wBAAA5D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC5F,CACb;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE;kBACN;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACFjI,OAAA,CAACjD,GAAG;oBAAC0K,OAAO,EAAC,MAAM;oBAAC8C,GAAG,EAAE,GAAI;oBAACoB,EAAE,EAAE,CAAE;oBAAA9D,QAAA,gBAClC7H,OAAA,CAACpC,OAAO;sBAACmN,KAAK,EAAC,YAAY;sBAAAlD,QAAA,eACzB7H,OAAA,CAAC5C,UAAU;wBACTyN,IAAI,EAAC,OAAO;wBACZnB,OAAO,EAAEA,CAAA,KAAM3E,uBAAuB,CAAC,MAAM,EAAE4B,OAAO,CAAE;wBACxDuB,EAAE,EAAE;0BACFK,KAAK,EAAE,SAAS;0BAChB,SAAS,EAAE;4BACTuB,eAAe,EAAE;0BACnB;wBACF,CAAE;wBAAAjC,QAAA,eAEF7H,OAAA,CAACL,QAAQ;0BAACkJ,QAAQ,EAAC;wBAAO;0BAAAf,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnB;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC,eACVjI,OAAA,CAACpC,OAAO;sBAACmN,KAAK,EAAC,UAAU;sBAAAlD,QAAA,eACvB7H,OAAA,CAAC5C,UAAU;wBACTyN,IAAI,EAAC,OAAO;wBACZnB,OAAO,EAAEA,CAAA,KAAM3E,uBAAuB,CAAC,MAAM,EAAE4B,OAAO,CAAE;wBACxDuB,EAAE,EAAE;0BACFK,KAAK,EAAE,SAAS;0BAChB,SAAS,EAAE;4BACTuB,eAAe,EAAE;0BACnB;wBACF,CAAE;wBAAAjC,QAAA,eAEF7H,OAAA,CAACvB,QAAQ;0BAACoK,QAAQ,EAAC;wBAAO;0BAAAf,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnB;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC,eACVjI,OAAA,CAACpC,OAAO;sBAACmN,KAAK,EAAC,SAAS;sBAAAlD,QAAA,eACtB7H,OAAA,CAAC5C,UAAU;wBACTyN,IAAI,EAAC,OAAO;wBACZnB,OAAO,EAAEA,CAAA,KAAM3C,mBAAmB,CAACJ,OAAO,CAAC7B,cAAc,CAAE;wBAC3DoD,EAAE,EAAE;0BACFK,KAAK,EAAE,SAAS;0BAChB,SAAS,EAAE;4BACTuB,eAAe,EAAE;0BACnB;wBACF,CAAE;wBAAAjC,QAAA,eAEF7H,OAAA,CAACrB,UAAU;0BAACkK,QAAQ,EAAC;wBAAO;0BAAAf,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrB;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CAAC;gBAAA,GA5FDtB,OAAO,CAAC7B,cAAc;kBAAAgD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA6FnB,CACX;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CACP;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACe,CAAC;UAAA,GA7NdnF,YAAY,CAAC4B,eAAe;YAAAoD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA8NxB,CACZ;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNjI,OAAA,CAAC3C,MAAM;MACLuO,IAAI,EAAEnK,sBAAuB;MAC7BoK,OAAO,EAAE5F,6BAA8B;MACvC6F,QAAQ,EAAC,IAAI;MACbC,SAAS;MACTC,UAAU,EAAE;QACV9D,EAAE,EAAE;UAAEmB,YAAY,EAAE;QAAE;MACxB,CAAE;MAAAxB,QAAA,gBAEF7H,OAAA,CAAC1C,WAAW;QAAC4K,EAAE,EAAE;UAAE+D,EAAE,EAAE;QAAE,CAAE;QAAApE,QAAA,eACzB7H,OAAA,CAAChD,UAAU;UAACqL,OAAO,EAAC,IAAI;UAACH,EAAE,EAAE;YAAEI,UAAU,EAAE;UAAI,CAAE;UAAAT,QAAA,EAC9ClG,sBAAsB,KAAK,QAAQ,GAAG,wBAAwB,GAAG;QAAuB;UAAAmG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/E;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACdjI,OAAA,CAACzC,aAAa;QAAAsK,QAAA,eACZ7H,OAAA,CAACjD,GAAG;UAACmL,EAAE,EAAE;YAAE8C,EAAE,EAAE;UAAE,CAAE;UAAAnD,QAAA,gBACjB7H,OAAA,CAACvC,SAAS;YACRsO,SAAS;YACTnB,KAAK,EAAC,mBAAmB;YACzBsB,KAAK,EAAEnK,oBAAoB,CAACE,iBAAkB;YAC9CkK,QAAQ,EAAG1B,CAAC,IAAKzI,uBAAuB,CAAC;cAAE,GAAGD,oBAAoB;cAAEE,iBAAiB,EAAEwI,CAAC,CAAC2B,MAAM,CAACF;YAAM,CAAC,CAAE;YACzGG,MAAM,EAAC,QAAQ;YACfC,QAAQ;YACRjE,OAAO,EAAC,UAAU;YAClBH,EAAE,EAAE;cAAEE,EAAE,EAAE;YAAE;UAAE;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eAEFjI,OAAA,CAACvC,SAAS;YACRsO,SAAS;YACTnB,KAAK,EAAC,OAAO;YACb2B,IAAI,EAAC,OAAO;YACZL,KAAK,EAAEnK,oBAAoB,CAACG,KAAM;YAClCiK,QAAQ,EAAG1B,CAAC,IAAKzI,uBAAuB,CAAC;cAAE,GAAGD,oBAAoB;cAAEG,KAAK,EAAEuI,CAAC,CAAC2B,MAAM,CAACF;YAAM,CAAC,CAAE;YAC7FG,MAAM,EAAC,QAAQ;YACfhE,OAAO,EAAC,UAAU;YAClBmE,UAAU,EAAC,uDAAuD;YAClEtE,EAAE,EAAE;cAAEE,EAAE,EAAE;YAAE;UAAE;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eAEFjI,OAAA,CAACvC,SAAS;YACRsO,SAAS;YACTnB,KAAK,EAAC,UAAU;YAChBsB,KAAK,EAAEnK,oBAAoB,CAACI,QAAS;YACrCgK,QAAQ,EAAG1B,CAAC,IAAKzI,uBAAuB,CAAC;cAAE,GAAGD,oBAAoB;cAAEI,QAAQ,EAAEsI,CAAC,CAAC2B,MAAM,CAACF;YAAM,CAAC,CAAE;YAChGG,MAAM,EAAC,QAAQ;YACfhE,OAAO,EAAC,UAAU;YAClBmE,UAAU,EAAC;UAA+C;YAAA1E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAChBjI,OAAA,CAACxC,aAAa;QAAC0K,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAE6C,EAAE,EAAE;QAAE,CAAE;QAAAnD,QAAA,gBACjC7H,OAAA,CAAC/C,MAAM;UACLyM,OAAO,EAAEzD,6BAA8B;UACvCiC,EAAE,EAAE;YAAEyB,aAAa,EAAE;UAAO,CAAE;UAAA9B,QAAA,EAC/B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTjI,OAAA,CAAC/C,MAAM;UACLyM,OAAO,EAAExD,wBAAyB;UAClCmC,OAAO,EAAC,WAAW;UACnBH,EAAE,EAAE;YACFyB,aAAa,EAAE,MAAM;YACrBrB,UAAU,EAAE,GAAG;YACfsB,EAAE,EAAE;UACN,CAAE;UAAA/B,QAAA,EAEDlG,sBAAsB,KAAK,QAAQ,GAAG,MAAM,GAAG;QAAO;UAAAmG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGTjI,OAAA,CAAC3C,MAAM;MACLuO,IAAI,EAAExJ,iBAAkB;MACxByJ,OAAO,EAAEjF,wBAAyB;MAClCkF,QAAQ,EAAC,IAAI;MACbC,SAAS;MACTC,UAAU,EAAE;QACV9D,EAAE,EAAE;UAAEmB,YAAY,EAAE;QAAE;MACxB,CAAE;MAAAxB,QAAA,gBAEF7H,OAAA,CAAC1C,WAAW;QAAC4K,EAAE,EAAE;UAAE+D,EAAE,EAAE;QAAE,CAAE;QAAApE,QAAA,eACzB7H,OAAA,CAAChD,UAAU;UAACqL,OAAO,EAAC,IAAI;UAACH,EAAE,EAAE;YAAEI,UAAU,EAAE;UAAI,CAAE;UAAAT,QAAA,EAC9CvF,iBAAiB,KAAK,MAAM,GAAG,kBAAkB,GAAG;QAAkB;UAAAwF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACdjI,OAAA,CAACzC,aAAa;QAAAsK,QAAA,eACZ7H,OAAA,CAACjD,GAAG;UAACmL,EAAE,EAAE;YAAE8C,EAAE,EAAE;UAAE,CAAE;UAAAnD,QAAA,EAChBvF,iBAAiB,KAAK,MAAM,IAAIE,eAAe,gBAC9CxC,OAAA,CAACnC,IAAI;YAAAgK,QAAA,gBACH7H,OAAA,CAAClC,QAAQ;cAAA+J,QAAA,eACP7H,OAAA,CAACjC,YAAY;gBACXoN,OAAO,EAAC,gBAAgB;gBACxBC,SAAS,EAAE5I,eAAe,CAACsC;cAAe;gBAAAgD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC,eACXjI,OAAA,CAAC3B,OAAO;cAAAyJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACXjI,OAAA,CAAClC,QAAQ;cAAA+J,QAAA,eACP7H,OAAA,CAACjC,YAAY;gBACXoN,OAAO,EAAC,MAAM;gBACdC,SAAS,EAAElE,mBAAmB,CAAC1E,eAAe,CAACI,YAAY;cAAE;gBAAAkF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9D;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC,eACXjI,OAAA,CAAC3B,OAAO;cAAAyJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACXjI,OAAA,CAAClC,QAAQ;cAAA+J,QAAA,eACP7H,OAAA,CAACjC,YAAY;gBACXoN,OAAO,EAAC,OAAO;gBACfC,SAAS,eACPpL,OAAA,CAAC7C,IAAI;kBACHyN,KAAK,EAAEpI,eAAe,CAAC8E,KAAM;kBAC7BiB,KAAK,EAAElB,aAAa,CAAC7E,eAAe,CAAC8E,KAAK,CAAE;kBAC5CuD,IAAI,EAAC;gBAAO;kBAAA/C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb;cACF;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC,eACXjI,OAAA,CAAC3B,OAAO;cAAAyJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACXjI,OAAA,CAAClC,QAAQ;cAAA+J,QAAA,eACP7H,OAAA,CAACjC,YAAY;gBACXoN,OAAO,EAAC,aAAa;gBACrBC,SAAS,EAAE5I,eAAe,CAACK,WAAW,IAAI;cAAsB;gBAAAiF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC,eACXjI,OAAA,CAAC3B,OAAO;cAAAyJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACXjI,OAAA,CAAClC,QAAQ;cAAA+J,QAAA,eACP7H,OAAA,CAACjC,YAAY;gBACXoN,OAAO,EAAC,aAAU;gBAClBC,SAAS,eACPpL,OAAA,CAAC7C,IAAI;kBACHyN,KAAK,EAAEpI,eAAe,CAACQ,QAAQ,IAAI,SAAU;kBAC7CuF,KAAK,EAAEf,gBAAgB,CAAChF,eAAe,CAACQ,QAAQ,IAAI,SAAS,CAAE;kBAC/D6H,IAAI,EAAC;gBAAO;kBAAA/C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb;cACF;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC,eACXjI,OAAA,CAAC3B,OAAO;cAAAyJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACXjI,OAAA,CAAClC,QAAQ;cAAA+J,QAAA,eACP7H,OAAA,CAACjC,YAAY;gBACXoN,OAAO,EAAC,cAAc;gBACtBC,SAAS,EAAE5I,eAAe,CAACM,YAAY,IAAI;cAAgB;gBAAAgF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5D;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC,EACVzF,eAAe,CAACS,kBAAkB,iBACjCjD,OAAA,CAAAE,SAAA;cAAA2H,QAAA,gBACE7H,OAAA,CAAC3B,OAAO;gBAAAyJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACXjI,OAAA,CAAClC,QAAQ;gBAAA+J,QAAA,eACP7H,OAAA,CAACjC,YAAY;kBACXoN,OAAO,EAAC,oBAAoB;kBAC5BC,SAAS,EAAE5I,eAAe,CAACS;gBAAmB;kBAAA6E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/C;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACM,CAAC;YAAA,eACX,CACH,eACDjI,OAAA,CAAC3B,OAAO;cAAAyJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACXjI,OAAA,CAAClC,QAAQ;cAAA+J,QAAA,eACP7H,OAAA,CAACjC,YAAY;gBACXoN,OAAO,EAAC,gBAAgB;gBACxBC,SAAS,EAAE,IAAIE,IAAI,CAAC9I,eAAe,CAAC6I,cAAc,CAAC,CAACE,kBAAkB,CAAC,OAAO;cAAE;gBAAAzD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC,EACVzF,eAAe,CAACO,aAAa,iBAC5B/C,OAAA,CAAAE,SAAA;cAAA2H,QAAA,gBACE7H,OAAA,CAAC3B,OAAO;gBAAAyJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACXjI,OAAA,CAAClC,QAAQ;gBAAA+J,QAAA,eACP7H,OAAA,CAACjC,YAAY;kBACXoN,OAAO,EAAC,eAAe;kBACvBC,SAAS,EAAE,IAAIE,IAAI,CAAC9I,eAAe,CAACO,aAAa,CAAC,CAACwI,kBAAkB,CAAC,OAAO;gBAAE;kBAAAzD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACM,CAAC;YAAA,eACX,CACH,eACDjI,OAAA,CAAC3B,OAAO;cAAAyJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACXjI,OAAA,CAAClC,QAAQ;cAAA+J,QAAA,eACP7H,OAAA,CAACjC,YAAY;gBACXoN,OAAO,EAAC,gBAAgB;gBACxBC,SAAS,EAAE5I,eAAe,CAACgJ,qBAAqB,IAAI;cAAE;gBAAA1D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC,eACXjI,OAAA,CAAC3B,OAAO;cAAAyJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACXjI,OAAA,CAAClC,QAAQ;cAAA+J,QAAA,eACP7H,OAAA,CAACjC,YAAY;gBACXoN,OAAO,EAAC,eAAe;gBACvBC,SAAS,EAAE,GAAG,CAAC5I,eAAe,CAACiJ,yBAAyB,IAAI,CAAC,EAAEC,OAAO,CAAC,CAAC,CAAC;cAAI;gBAAA5D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9E;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CAAC,gBAEPjI,OAAA,CAAAE,SAAA;YAAA2H,QAAA,gBACE7H,OAAA,CAACvC,SAAS;cACRsO,SAAS;cACTU,MAAM;cACN7B,KAAK,EAAC,cAAc;cACpBsB,KAAK,EAAExJ,eAAe,CAACE,YAAa;cACpCuJ,QAAQ,EAAG1B,CAAC,IAAK9H,kBAAkB,CAAC;gBAAE,GAAGD,eAAe;gBAAEE,YAAY,EAAE6H,CAAC,CAAC2B,MAAM,CAACF;cAAM,CAAC,CAAE;cAC1FG,MAAM,EAAC,QAAQ;cACfnE,EAAE,EAAE;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAP,QAAA,gBAEd7H,OAAA,CAAC5B,QAAQ;gBAAC8N,KAAK,EAAC,MAAM;gBAAArE,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACtCjI,OAAA,CAAC5B,QAAQ;gBAAC8N,KAAK,EAAC,uBAAuB;gBAAArE,QAAA,EAAC;cAAqB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACxEjI,OAAA,CAAC5B,QAAQ;gBAAC8N,KAAK,EAAC,qBAAqB;gBAAArE,QAAA,EAAC;cAAmB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACpEjI,OAAA,CAAC5B,QAAQ;gBAAC8N,KAAK,EAAC,gBAAgB;gBAAArE,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAC1DjI,OAAA,CAAC5B,QAAQ;gBAAC8N,KAAK,EAAC,SAAS;gBAAArE,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC,eAEZjI,OAAA,CAACvC,SAAS;cACRsO,SAAS;cACTU,MAAM;cACN7B,KAAK,EAAC,aAAU;cAChBsB,KAAK,EAAExJ,eAAe,CAACM,QAAS;cAChCmJ,QAAQ,EAAG1B,CAAC,IAAK9H,kBAAkB,CAAC;gBAAE,GAAGD,eAAe;gBAAEM,QAAQ,EAAEyH,CAAC,CAAC2B,MAAM,CAACF;cAAM,CAAC,CAAE;cACtFG,MAAM,EAAC,QAAQ;cACfnE,EAAE,EAAE;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAP,QAAA,gBAEd7H,OAAA,CAAC5B,QAAQ;gBAAC8N,KAAK,EAAC,OAAO;gBAAArE,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACxCjI,OAAA,CAAC5B,QAAQ;gBAAC8N,KAAK,EAAC,SAAS;gBAAArE,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAC5CjI,OAAA,CAAC5B,QAAQ;gBAAC8N,KAAK,EAAC,MAAM;gBAAArE,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACtCjI,OAAA,CAAC5B,QAAQ;gBAAC8N,KAAK,EAAC,SAAS;gBAAArE,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC,eAEZjI,OAAA,CAACvC,SAAS;cACRsO,SAAS;cACTnB,KAAK,EAAC,aAAa;cACnBsB,KAAK,EAAExJ,eAAe,CAACG,WAAY;cACnCsJ,QAAQ,EAAG1B,CAAC,IAAK9H,kBAAkB,CAAC;gBAAE,GAAGD,eAAe;gBAAEG,WAAW,EAAE4H,CAAC,CAAC2B,MAAM,CAACF;cAAM,CAAC,CAAE;cACzFG,MAAM,EAAC,QAAQ;cACfK,SAAS;cACTC,IAAI,EAAE,CAAE;cACRzE,EAAE,EAAE;gBAAEE,EAAE,EAAE;cAAE;YAAE;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,eAEFjI,OAAA,CAACvC,SAAS;cACRsO,SAAS;cACTnB,KAAK,EAAC,cAAc;cACpBsB,KAAK,EAAExJ,eAAe,CAACI,YAAa;cACpCqJ,QAAQ,EAAG1B,CAAC,IAAK9H,kBAAkB,CAAC;gBAAE,GAAGD,eAAe;gBAAEI,YAAY,EAAE2H,CAAC,CAAC2B,MAAM,CAACF;cAAM,CAAC,CAAE;cAC1FG,MAAM,EAAC,QAAQ;cACfC,QAAQ;cACRE,UAAU,EAAC,0CAAuC;cAClDtE,EAAE,EAAE;gBAAEE,EAAE,EAAE;cAAE;YAAE;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,eAEFjI,OAAA,CAACvC,SAAS;cACRsO,SAAS;cACTnB,KAAK,EAAC,oBAAoB;cAC1BsB,KAAK,EAAExJ,eAAe,CAACO,kBAAmB;cAC1CkJ,QAAQ,EAAG1B,CAAC,IAAK9H,kBAAkB,CAAC;gBAAE,GAAGD,eAAe;gBAAEO,kBAAkB,EAAEwH,CAAC,CAAC2B,MAAM,CAACF;cAAM,CAAC,CAAE;cAChGG,MAAM,EAAC,QAAQ;cACfK,SAAS;cACTC,IAAI,EAAE,CAAE;cACRH,UAAU,EAAC,2CAA2C;cACtDtE,EAAE,EAAE;gBAAEE,EAAE,EAAE;cAAE;YAAE;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,eAEFjI,OAAA,CAACvC,SAAS;cACRsO,SAAS;cACTnB,KAAK,EAAC,eAAe;cACrB2B,IAAI,EAAC,MAAM;cACXL,KAAK,EAAExJ,eAAe,CAACK,aAAc;cACrCoJ,QAAQ,EAAG1B,CAAC,IAAK9H,kBAAkB,CAAC;gBAAE,GAAGD,eAAe;gBAAEK,aAAa,EAAE0H,CAAC,CAAC2B,MAAM,CAACF;cAAM,CAAC,CAAE;cAC3FG,MAAM,EAAC,QAAQ;cACfO,eAAe,EAAE;gBACfC,MAAM,EAAE;cACV;YAAE;cAAA/E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA,eACF;QACH;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAChBjI,OAAA,CAACxC,aAAa;QAAC0K,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAE6C,EAAE,EAAE;QAAE,CAAE;QAAAnD,QAAA,gBACjC7H,OAAA,CAAC/C,MAAM;UACLyM,OAAO,EAAE9C,wBAAyB;UAClCsB,EAAE,EAAE;YAAEyB,aAAa,EAAE;UAAO,CAAE;UAAA9B,QAAA,EAE7BvF,iBAAiB,KAAK,MAAM,GAAG,QAAQ,GAAG;QAAS;UAAAwF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C,CAAC,EACR3F,iBAAiB,KAAK,MAAM,iBAC3BtC,OAAA,CAAC/C,MAAM;UACLyM,OAAO,EAAE7C,mBAAoB;UAC7BwB,OAAO,EAAC,WAAW;UACnBH,EAAE,EAAE;YACFyB,aAAa,EAAE,MAAM;YACrBrB,UAAU,EAAE,GAAG;YACfsB,EAAE,EAAE;UACN,CAAE;UAAA/B,QAAA,EACH;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGTjI,OAAA,CAACF,kBAAkB;MACjBM,UAAU,EAAEA,UAAW;MACvBwL,IAAI,EAAE3K,eAAgB;MACtB4K,OAAO,EAAEA,CAAA,KAAM3K,kBAAkB,CAAC,KAAK,CAAE;MACzC4L,SAAS,EAAEA,CAAC/I,QAAQ,EAAEgJ,cAAc,KAAK;QACvC3J,OAAO,CAACgB,GAAG,CAAC,iDAAiD,CAAC;;QAE9D;QACA,IAAI2I,cAAc,EAAE;UAClB;UACA3J,OAAO,CAACgB,GAAG,CAAC,cAAc,EAAE2I,cAAc,CAAC;QAC7C;;QAEA;QACA7J,WAAW,CAAC,CAAC;QACbG,eAAe,CAAC,CAAC;QACjBG,gBAAgB,CAAC,CAAC;QAClBtC,kBAAkB,CAAC,KAAK,CAAC;QAEzBkC,OAAO,CAACgB,GAAG,CAAC,0BAA0B,CAAC;MACzC;IAAE;MAAA0D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAAC3H,EAAA,CAnkCIH,wBAAwB;EAAA,QAEYrD,eAAe;AAAA;AAAAkQ,EAAA,GAFnD7M,wBAAwB;AAqkC9B,eAAeA,wBAAwB;AAAC,IAAA6M,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}