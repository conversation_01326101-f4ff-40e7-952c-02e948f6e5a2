#!/usr/bin/env python3
"""
Test creazione comanda con nuovo sistema di codici
"""

import sys
import os
sys.path.append('.')
sys.path.append('./modules')

def test_creazione_comande():
    """Test creazione di comande reali per verificare la progressione"""
    print("🧪 === TEST CREAZIONE COMANDE CON NUOVI CODICI ===")
    
    try:
        from modules.comande_new import crea_comanda
        
        # Parametri di test
        id_cantiere = 1
        responsabile = "Test Responsabile"
        
        print(f"📋 Test con cantiere {id_cantiere}, responsabile '{responsabile}'")
        
        # Crea diverse comande per testare la progressione
        comande_create = []
        
        tipi_test = [
            ('POSA', 'Test comanda posa 1'),
            ('POSA', 'Test comanda posa 2'),
            ('COLLEGAMENTO_PARTENZA', 'Test collegamento partenza'),
            ('POSA', 'Test comanda posa 3'),
            ('CERTIFICAZIONE', 'Test certificazione'),
        ]
        
        print("\n🚀 Creazione comande di test...")
        
        for tipo, descrizione in tipi_test:
            try:
                codice = crea_comanda(
                    id_cantiere=id_cantiere,
                    tipo_comanda=tipo,
                    descrizione=descrizione,
                    responsabile=responsabile
                )
                
                if codice:
                    comande_create.append((tipo, codice))
                    print(f"   ✅ {tipo:20} → {codice}")
                else:
                    print(f"   ❌ {tipo:20} → Errore nella creazione")
                    
            except Exception as e:
                print(f"   ❌ {tipo:20} → Errore: {e}")
        
        print(f"\n📊 Risultati:")
        print(f"   - Comande create: {len(comande_create)}")
        
        # Verifica progressione per tipo
        print(f"\n🔍 Verifica progressione per tipo:")
        
        tipi_conteggio = {}
        for tipo, codice in comande_create:
            if tipo not in tipi_conteggio:
                tipi_conteggio[tipo] = []
            tipi_conteggio[tipo].append(codice)
        
        for tipo, codici in tipi_conteggio.items():
            print(f"   {tipo}:")
            for i, codice in enumerate(codici):
                print(f"     {i+1}. {codice}")
            
            # Verifica che i numeri siano progressivi
            numeri = [int(c[3:]) for c in codici]
            progressivo = all(numeri[i] <= numeri[i+1] for i in range(len(numeri)-1))
            print(f"     → Progressione corretta: {'✅' if progressivo else '❌'}")
        
        return len(comande_create) > 0
        
    except Exception as e:
        print(f"❌ Errore durante il test: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_formato_codici():
    """Test del formato dei codici generati"""
    print("\n🔍 === TEST FORMATO CODICI ===")
    
    # Esempi di codici che dovrebbero essere generati
    esempi_codici = [
        "POS001", "POS002", "POS010", "POS099",
        "CPT001", "CPT005", "CPT020",
        "CAR001", "CAR003",
        "CER001", "CER007"
    ]
    
    print("📋 Verifica formato codici:")
    
    for codice in esempi_codici:
        # Verifica formato
        lunghezza_ok = len(codice) == 6
        prefisso_ok = codice[:3].isalpha() and codice[:3].isupper()
        numero_ok = codice[3:].isdigit() and len(codice[3:]) == 3
        
        formato_ok = lunghezza_ok and prefisso_ok and numero_ok
        
        print(f"   {codice}: {'✅' if formato_ok else '❌'}")
        if not formato_ok:
            print(f"     - Lunghezza: {'✅' if lunghezza_ok else '❌'} ({len(codice)})")
            print(f"     - Prefisso: {'✅' if prefisso_ok else '❌'} ({codice[:3]})")
            print(f"     - Numero: {'✅' if numero_ok else '❌'} ({codice[3:]})")
    
    print(f"\n✅ Vantaggi del nuovo formato:")
    print(f"   - Lunghezza fissa: 6 caratteri")
    print(f"   - Facile da leggere: POS001 vs POS_1_20250610065219188")
    print(f"   - Facile da dire: 'P-O-S zero-zero-uno'")
    print(f"   - Ordinamento naturale: POS001, POS002, POS003...")
    print(f"   - Compatibile con sistemi legacy")

def main():
    print("🚀 AVVIO TEST SISTEMA CODICI COMANDA SEMPLIFICATO")
    print("="*60)
    
    # Test 1: Creazione comande reali
    test1_ok = test_creazione_comande()
    
    # Test 2: Formato codici
    test_formato_codici()
    
    # Risultati
    print("\n" + "="*60)
    print("📋 RIEPILOGO TEST")
    print("="*60)
    print(f"✅ Creazione comande: {'PASS' if test1_ok else 'FAIL'}")
    
    if test1_ok:
        print("\n🎉 SISTEMA CODICI SEMPLIFICATO FUNZIONANTE!")
        print("\n📝 CONFRONTO:")
        print("   PRIMA: POS_1_20250610065219188 (21 caratteri)")
        print("   DOPO:  POS001                  (6 caratteri)")
        print("\n✅ Benefici:")
        print("   - 71% di riduzione lunghezza")
        print("   - Più facile da leggere e memorizzare")
        print("   - Numerazione progressiva intuitiva")
        print("   - Migliore UX nell'interfaccia")
        print("   - Più adatto per comunicazione verbale")
        
        print("\n🔄 Le comande esistenti continueranno a funzionare normalmente")
        print("🆕 Le nuove comande useranno il formato semplificato")
        
    else:
        print("\n❌ PROBLEMI RILEVATI!")
        print("Verificare la configurazione del database.")
    
    return test1_ok

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
